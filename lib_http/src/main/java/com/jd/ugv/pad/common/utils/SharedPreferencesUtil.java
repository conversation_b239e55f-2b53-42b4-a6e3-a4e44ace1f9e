package com.jd.ugv.pad.common.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.annotations.NonNull;

/**
 * Created by yusiyang on 2018/2/11.
 * Modified by lixiao on 2021/09/07
 */

public class SharedPreferencesUtil {
    private static final String TAG = "SharedPreferencesUtil";
    private static SharedPreferencesUtil instance = new SharedPreferencesUtil();
    private static SharedPreferences sp;

    private SharedPreferencesUtil() {
    }

    public static SharedPreferencesUtil getInstance() {
        return instance;
    }

    public void init(Context context, @NonNull String key) {
        sp = context.getSharedPreferences(key, Context.MODE_PRIVATE);
        sp.registerOnSharedPreferenceChangeListener(listener);
    }

    public void unInit() {
        sp.unregisterOnSharedPreferenceChangeListener(listener);
    }


    public void setInt(String key, int value) {
        sp.edit().putInt(key, value).apply();
    }

    public int getInt(String key) {
        return sp.getInt(key, 0);
    }

    public void setLong(String key, long value) {
        sp.edit().putLong(key, value).apply();
    }

    public long getLong(String key) {
        return sp.getLong(key, 0);
    }

    public void setString(String key, String value) {
        sp.edit().putString(key, value).apply();
    }

    public String getString(String key) {
        return sp.getString(key, "");
    }

    public String getString(String key, String defaultValue) {
        return sp.getString(key, defaultValue);
    }

    public void setBoolean(String key, boolean value) {
        sp.edit().putBoolean(key, value).apply();
    }

    public boolean getBoolean(String key) {
        return getBoolean(key, false);
    }

    public boolean getBoolean(String key, boolean default_value) {
        return sp.getBoolean(key, default_value);
    }

    public void remove(String key) {
        sp.edit().remove(key).apply();
    }

    public void clear() {
        sp.edit().clear().apply();
    }

    public boolean isKeyExit(String key) {
        return sp.contains(key);
    }

    public interface PreferenceChangeListener {
        void onSharedPreferenceChanged(SharedPreferences sp, String key);
    }

    private ArrayList<PreferenceChangeListener> listeners = new ArrayList<>();

    public void registerListener(PreferenceChangeListener listener) {
        listeners.add(listener);
    }

    public void unRegisterListener(PreferenceChangeListener listener) {
        listeners.remove(listener);
    }

    private SharedPreferences.OnSharedPreferenceChangeListener listener = (sharedPreferences, key) -> {
        for (PreferenceChangeListener listener : listeners) {
            listener.onSharedPreferenceChanged(sharedPreferences, key);
        }
    };


    public static void putHashMapData(String key, Map<String, String> datas) {
        JSONArray mJsonArray = new JSONArray();
        Iterator<Map.Entry<String, String>> iterator = datas.entrySet().iterator();

        JSONObject object = new JSONObject();

        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            try {
                object.put(entry.getKey(), entry.getValue());
            } catch (JSONException e) {

            } finally {}
        }
        mJsonArray.put(object);

        SharedPreferences.Editor editor = sp.edit();
        editor.putString(key, mJsonArray.toString());
        editor.commit();
    }

    public static Map<String, String> getHashMapData(String key) {
        Map<String, String> datas = new HashMap<>();
        String result = sp.getString(key, "");
        try {
            JSONArray array = new JSONArray(result);
            for (int i = 0; i < array.length(); i++) {
                JSONObject itemObject = array.getJSONObject(i);
                JSONArray names = itemObject.names();
                if (names != null) {
                    for (int j = 0; j < names.length(); j++) {
                        String name = names.getString(j);
                        String value = itemObject.getString(name);
                        datas.put(name, value);
                    }
                }
            }
        } catch (JSONException e) {

        } finally {}

        return datas;
    }

    public static <T> void saveObject(String key, T data){
        if(data == null){
            return;
        }
        Gson gson = new Gson();
        String json = gson.toJson(data);
        SharedPreferences.Editor edit = sp.edit();
        edit.putString(key, json);
        edit.apply();
    }

    public static <T> T getObject(String key, Class<T> cls){
        T data = null;
        String json = sp.getString(key, null);
        if(json == null){
            return null;
        }
        try{
            Gson gson = new Gson();
            JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
            data = gson.fromJson(jsonObject, cls);
        }catch (Exception e){
            Log.e(key,"get sp object error: " + e);
        }
        return data;
    }
}
