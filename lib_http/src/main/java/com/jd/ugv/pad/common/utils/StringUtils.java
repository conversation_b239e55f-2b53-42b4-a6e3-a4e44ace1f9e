/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 ***************************************************************************/

package com.jd.ugv.pad.common.utils;

import android.util.Log;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * String util class
 * </p>
 * Created by xiaowei9 on 2018/10/1.
 */
public class StringUtils {

    private static final String TAG = "StringUtils";

    /**
     * <p>
     * check string is blank or not
     * </p>
     *
     * @param str string
     * @return true or false
     */
    public static boolean isBlank(String str) {
        int strLen;
        if (str == null || (strLen = str.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((Character.isWhitespace(str.charAt(i)) == false)) {
                return false;
            }
        }
        return true;
    }

    /**
     * <p>
     * replace string
     * </p>
     *
     * @param str string
     * @return new string
     */
    public static String replaceName(String str) {
        int length = str.length();
        if (length == 2) {
            return str.charAt(0) + "*";
        } else if (length == 1) {
            return str;
        }

        String reg = ".{1}";
        StringBuffer st = new StringBuffer();
        Pattern p = Pattern.compile(reg);
        Matcher m = p.matcher(str);
        int i = 0;
        while (m.find()) {
            i++;
            if (i == 1 || i == str.length()) {
                continue;
            }
            m.appendReplacement(st, "*");
        }
        m.appendTail(st);

        return st.toString();
    }

    /**
     * <p>
     * time translate String to long.
     * </p>
     *
     * @param dateStr date string.
     * @param format eg：yyyy-MM-dd HH:mm:ss
     * @return timestamp
     */
    public static long date2TimeStamp(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.getDefault());
            return sdf.parse(dateStr).getTime();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "format [" + dateStr + "] error because : " + e.getMessage());
        } finally {

        }
        return 0L;
    }

    /**
     * <p>
     * time translate String to long.
     * </p>
     *
     * @param timestamp system timestamp.
     * @param format eg：yyyy-MM-dd HH:mm:ss
     * @return timestamp
     */
    public static String timeStamp2String(long timestamp, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.getDefault());
            return sdf.format(new Date(timestamp));
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "format [" + timestamp + "] error because : " + e.getMessage());
        } finally {

        }

        return null;
    }

    /**
     * 提取字符串中的数字
     * @param input 输入字符串
     * @return 提取出的数字字符串
     */
    public static String extractIntegers(String input) {
        StringBuilder numbers = new StringBuilder();

        for (char c : input.toCharArray()) {
            if (Character.isDigit(c)) {
                numbers.append(c);
            }
        }

        return numbers.toString();
    }

    /**
     * 转换日期时间
     * @param time
     * @return
     */
    public static String getDateTime(String time){
        DateTimeFormatter originalFormatter = null;
        try{
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                originalFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                // 解析原始日期字符串
                LocalDateTime dateTime = LocalDateTime.parse(time, originalFormatter);

                // 定义目标日期格式
                DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern("MM月dd日 HH:mm");

                // 格式化为目标字符串
                return dateTime.format(targetFormatter);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return time;
    }

}
