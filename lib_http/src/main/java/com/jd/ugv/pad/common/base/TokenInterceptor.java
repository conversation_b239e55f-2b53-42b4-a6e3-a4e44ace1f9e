/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jd.ugv.pad.common.base;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.ugv.pad.common.upgrade.OkHttpManager;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.MqttUtils;

import java.io.IOException;
import java.lang.reflect.Type;

import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <p>
 * token interceptor
 * </p >
 *
 * <AUTHOR>
 * @date： 2022/5/9
 */
public class TokenInterceptor implements Interceptor {
    private static final String TAG = "TokenInterceptor";
    private TokenData data;

    public TokenInterceptor(TokenData data){
        this.data = data;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        if(data != null && data.getToken() != null) {
            LogUtils.i(TAG, "token: " + data.toString());
            long curTime = System.currentTimeMillis();
            if(curTime >= data.getExpiresInTime()){
                //更新token
                LogUtils.i(TAG,"==========更新Token=========");
                getTokenData();
            }

            Request oldRequest = chain.request();
            Request newRequest = oldRequest.newBuilder()
                    .addHeader("token", data.getToken())
                    .addHeader("authType", Integer.toString(3))
                    .addHeader("RequestId", MqttUtils.getRequestId()+"")
                    .addHeader("LOP-DN", data.getLopDn())
                    .method(oldRequest.method(), oldRequest.body())
                    .build();
            return chain.proceed(newRequest);
        }else{
            return chain.proceed(chain.request());
        }
    }

    private void getTokenData(){
        Headers headers = new Headers.Builder().add("LOP-DN", data.getLopDn()).build();
        TokenRequestBody body = new TokenRequestBody(data.getRobotSn(), data.getApkKey());
        Gson gson = new Gson();
        String response = OkHttpManager.getInstance().postNoCallback(gson.toJson(body), headers, this.data.getUrl());
        if(response != null) {
            try {
                LogUtils.i(TAG,"getTokenData===>response: " + response);
                Type type = new TypeToken<BaseResult<TokenInfoData>>() {}.getType();
                BaseResult<TokenInfoData> data = gson.fromJson(response, type);
                if(data != null && data.isSuccess() && data.getData() != null){
                    this.data.setToken(data.getData().getAccessToken());
                    long time = data.getData().getExpiresIn();
                    time = System.currentTimeMillis() + (time - 1000) * 1000;
                    this.data.setExpiresInTime(time);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }
}
