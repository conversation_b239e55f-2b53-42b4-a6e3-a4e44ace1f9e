/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 ***************************************************************************/

package com.jd.ugv.pad.common.upgrade;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jd.ugv.pad.common.utils.LogUtils;

import java.io.File;
import java.io.IOException;
import java.net.FileNameMap;
import java.net.SocketTimeoutException;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.ConnectionPool;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Created by huangfangyi/qq84543217 on 2016/6/30.
 * for download apk file for uograde
 */
public class OkHttpManager {
    private static final String TAG = "OkHttpManager";
    public static Context context;
    public static OkHttpManager serverTask;
    private static OkHttpClient okHttpClient;
    private static final int RESULT_ERROR = 1000;
    private static final int RESULT_SUCESS = 2000;

    public OkHttpManager(Context context) {
        this.context = context;
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(32, 10, TimeUnit.SECONDS))
                .build();
    }

    public static synchronized void init(Context context) {
        if (serverTask == null) {
            serverTask = new OkHttpManager(context);
        }
    }

    public static OkHttpManager getInstance() {
        if (serverTask == null) {
            Log.e(TAG, "please init first!");
        }
        return serverTask;
    }

    //纯粹键值对post请求
    public void post(List<Param> params, String url, HttpCallBack httpCallBack) {
        Log.d(TAG, "url----->>");
        FormBody.Builder bodyBulder = new FormBody.Builder();
        for (Param param : params) {
            bodyBulder.add(param.getKey(), param.getValue());
            Log.d(TAG, "param.getKey()----->>" + param.getKey());
            Log.d(TAG, "param.getValue()----->>" + param.getValue());
        }
        RequestBody requestBody = bodyBulder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        startRequest(request, httpCallBack);

    }

    //纯粹键值对post请求
    public void get(Map<String, Object> params, List<Param> headers, String url, HttpCallBack httpCallBack) {
        url = url + getBodyParams(params);
        Log.i(TAG, "url----->>"+url);
        Headers.Builder hds = new Headers.Builder();
        for (Param header : headers) {
            LogUtils.i(TAG, "header.getKey()------>" + header.getKey());
            LogUtils.i(TAG, "header.getvalue()------>" + header.getValue());
            hds.add(header.getKey(), header.getValue());
        }
        Request request = new Request.Builder()
                .url(url)
                .get()
                .headers(hds.build())
                .build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (httpCallBack != null && e.getMessage() != null) {
                    Log.e(TAG, "okhttp exception", e);
                    if (e instanceof SocketTimeoutException) {
                        Log.d(TAG, "okhttp release pool");
                        try {
                            okHttpClient.dispatcher().cancelAll();
                            okHttpClient.connectionPool().evictAll();
                        } catch (Exception ex) {
                            Log.e(TAG, "release pool error", ex);
                        }
                    }

                    httpCallBack.onFailure(e.getMessage().toString());
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (httpCallBack != null) {
                    try {
                        httpCallBack.onResponse(response);
                    } catch (Exception e) {
                        LogUtils.e(TAG, "startRequest error: " + e.getMessage());
                        httpCallBack.onFailure(e.getMessage());
                    }
                }
            }
        });

    }

    /**
     * 添加参数
     *
     * @param params
     * @return
     */
    private String getBodyParams(Map<String, Object> params) {
        //1.添加请求参数
        //遍历map中所有参数到builder
        if (params != null) {
            StringBuffer stringBuffer = new StringBuffer("?");
            for (String key : params.keySet()) {
                if (!TextUtils.isEmpty(key) && params.get(key) != null) {
                    //如果参数不是null并且不是""，就拼接起来
                    stringBuffer.append("&");
                    stringBuffer.append(key);
                    stringBuffer.append("=");
                    stringBuffer.append(params.get(key));
                }
            }
            return stringBuffer.toString();
        } else {
            return "";
        }
    }

    public String postNoCallback(String json, Headers headers, String url) {
        Log.d(TAG, "url----->>");
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), json);
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();


        try {
            return startRequestForToken(request);
        } catch (IOException e) {
            Log.e(TAG, "postNoCallback: error: " + e.getMessage());
        }
        return null;
    }

    //键值对+文件 post请求
    public void post(List<Param> params, List<File> files, String url, HttpCallBack httpCallBack) {
        Log.d(TAG, "url----->>");
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (Param param : params) {

            builder.addPart(Headers.of("Content-Disposition", "form-data; name=\"" + param.getKey() + "\""), RequestBody.create(MediaType.parse(guessMimeType(param.getKey())), param.getValue()));
            Log.d(TAG, "param.getKey()----->>" + param.getKey());
            Log.d(TAG, "param.getValue()----->>" + param.getValue());
        }
        for (File file : files) {
            if (file != null && file.exists()) {

                //TODO-本项目固化文件的键名为“file”
                builder.addPart(Headers.of("Content-Disposition",
                                "form-data; name=\"" + "file" + "\"; filename=\"" + file.getName() + "\""),
                        RequestBody.create(MediaType.parse(guessMimeType(file.getName())), file));

                Log.d(TAG, "file.getName()----->>" + file.getName());
            }

        }
        RequestBody requestBody = builder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        startRequest(request, httpCallBack);

    }

    /**
     * 上传文件
     * @param file
     * @param url
     * @param httpCallBack
     */
    public void uploadFile(File file, String url, HttpCallBack httpCallBack) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("application/zip"), file))
                .build();

        Request request = new Request.Builder()
                .url(url)
                .put(requestBody)
                .build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (httpCallBack != null && e.getMessage() != null) {
                    Log.e(TAG, "okhttp exception", e);
                    if (e instanceof SocketTimeoutException) {
                        Log.d(TAG, "okhttp release pool");
                        try {
                            okHttpClient.dispatcher().cancelAll();
                            okHttpClient.connectionPool().evictAll();
                        } catch (Exception ex) {
                            Log.e(TAG, "release pool error", ex);
                        }
                    }

                    httpCallBack.onFailure(e.getMessage().toString());
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (httpCallBack != null) {
                    int code = response.code();
                    if(code == 200){
                        httpCallBack.onResponse(new JsonObject());
                    }else{
                        httpCallBack.onFailure(response.message());
                    }
                }
            }
        });
    }

    //键值对+文件 post请求
    public void postMoments(List<Param> params, List<Uri> images, String url, HttpCallBack httpCallBack) {
        Log.d(TAG, "url----->>");
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);

        int num = images.size();
        String imageStr = "0";
        for (int i = 0; i < num; i++) {
            String imageUrl = images.get(i).getPath();
            String filename = imageUrl.substring(imageUrl
                    .lastIndexOf("/") + 1);

            File file = new File("/sdcard/bizchat/" + filename);

            File file_big = new File("/sdcard/bizchat/" + "big_" + filename);

            builder.addPart(Headers.of("Content-Disposition",
                            "form-data; name=\"" + "file_" + String.valueOf(i) + "\"; filename=\"" + file.getName() + "\""),
                    RequestBody.create(MediaType.parse(guessMimeType(file.getName())), file));


            // 大图
            builder.addPart(Headers.of("Content-Disposition",
                            "form-data; name=\"" + "file_" + String.valueOf(i) + "_big" + "\"; filename=\"" + file_big.getName() + "\""),
                    RequestBody.create(MediaType.parse(guessMimeType(file_big.getName())), file_big));

            if (i == 0) {
                imageStr = filename;
            } else {
                imageStr = imageStr + "split" + filename;
                Log.e(TAG, "imageStr---->>>>>>." + imageStr);
            }
        }
        params.add(new Param("num", String.valueOf(images.size())));
        params.add(new Param("imageStr", imageStr));
        /*   params.add(new Param("userID", DemoHelper.getInstance().getCurrentUsernName()));*/
        for (Param param : params) {

            builder.addPart(Headers.of("Content-Disposition", "form-data; name=\"" + param.getKey() + "\""), RequestBody.create(MediaType.parse(guessMimeType(param.getKey())), param.getValue()));
            Log.d(TAG, "param.getKey()----->>" + param.getKey());
            Log.d(TAG, "param.getValue()----->>" + param.getValue());
        }
        RequestBody requestBody = builder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        startRequest(request, httpCallBack);

    }

    private void startRequest(Request request, final HttpCallBack httpCallBack) {

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (httpCallBack != null && e.getMessage() != null) {
                    Log.e(TAG, "okhttp exception", e);
                    if (e instanceof SocketTimeoutException) {
                        Log.d(TAG, "okhttp release pool");
                        try {
                            okHttpClient.dispatcher().cancelAll();
                            okHttpClient.connectionPool().evictAll();
                        } catch (Exception ex) {
                            Log.e(TAG, "release pool error", ex);
                        }
                    }

                    httpCallBack.onFailure(e.getMessage().toString());
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (httpCallBack != null) {
                    String result = response.body().string();
                    try {
                        JsonObject jsonObject = JsonParser.parseString(result).getAsJsonObject();
                        httpCallBack.onResponse(jsonObject);
                    } catch (Exception e) {
                        LogUtils.e(TAG, "startRequest error: " + e.getMessage());
                        httpCallBack.onFailure(e.getMessage());
                    }
                }
            }
        });
    }

    private String startRequestForToken(Request request) throws IOException {
        return okHttpClient.newCall(request).execute().body().string();
    }

    public interface HttpCallBack {

        void onResponse(JsonObject jsonObject);

        void onResponse(Response response);

        void onFailure(String errorMsg);
    }

    private String guessMimeType(String path) {
        FileNameMap fileNameMap = URLConnection.getFileNameMap();
        String contentTypeFor = fileNameMap.getContentTypeFor(path);
        if (contentTypeFor == null) {
            contentTypeFor = "application/octet-stream";
        }
        return contentTypeFor;
    }

    public void get(List<Param> params, String url, HttpCallBack httpCallBack) {
        Log.d(TAG, "url----->>");
        String final_url = url;
        if (params.isEmpty() == false) {
            final_url = final_url + "?";
        }
        for (int i = 0; i < params.size(); i++) {
            Param param = params.get(i);
            final_url += (param.getKey() + "=" + param.getValue());
            if (i != params.size() - 1) {
                final_url = final_url + "&";
            }
        }

        Request request = new Request.Builder()
                .url(final_url)
                .get()
                .build();

        startRequest(request, httpCallBack);

    }

    public <T> void downLoadFile(String url, int downloadLength, int contentLength, final HttpCallBack httpCallBack) {
        Request request = null;
        if (downloadLength != 0 && contentLength != 0) {
            request = new Request.Builder()
                    .addHeader("RANGE", "bytes=" + downloadLength + "-" + contentLength)
                    .url(url)
                    .build();
        } else {
            request = new Request.Builder()
                    .url(url)
                    .build();
        }

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (httpCallBack != null) {
                    httpCallBack.onFailure(e.getMessage().toString());
                }
            }

            @Override
            public void onResponse(Call call, final Response response) throws IOException {
                httpCallBack.onResponse(response);
                ;
            }
        });
    }
}
