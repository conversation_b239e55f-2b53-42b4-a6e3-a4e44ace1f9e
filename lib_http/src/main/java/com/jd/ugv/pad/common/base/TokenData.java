/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jd.ugv.pad.common.base;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * token data
 * </p >
 *
 * <AUTHOR>
 * @date： 2022/5/10
 */
public class TokenData {

    //token value
    private String token;
    //vehicle name
    private String robotSn;

    private String apkKey;

    private String url;

    private String lopDn;

    private long expiresInTime;

    public TokenData(){

    }

    public TokenData(String token, String robotSn) {
        this.token = token;
        this.robotSn = robotSn;
    }

    public TokenData(String token, String robotSn, long expiresInTime, String lopDn) {
        this.token = token;
        this.robotSn = robotSn;
        this.expiresInTime = System.currentTimeMillis() + (expiresInTime - 1000) * 1000;
        this.lopDn = lopDn;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRobotSn() {
        return robotSn;
    }

    public void setRobotSn(String robotSn) {
        this.robotSn = robotSn;
    }

    public long getExpiresInTime() {
        return expiresInTime;
    }

    public void setExpiresInTime(long expiresInTime) {
        this.expiresInTime = expiresInTime;
    }

    public String getApkKey() {
        return apkKey;
    }

    public void setApkKey(String apkKey) {
        this.apkKey = apkKey;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getLopDn() {
        return lopDn;
    }

    public void setLopDn(String lopDn) {
        this.lopDn = lopDn;
    }

    @Override
    public String toString() {
        return "TokenData{" +
                ", token='" + token + '\'' +
                ", robotSn='" + robotSn + '\'' +
                ", apkKey='" + apkKey + '\'' +
                ", url='" + url + '\'' +
                ", lopDn='" + lopDn + '\'' +
                ", expiresInTime=" + expiresInTime +
                '}';
    }
}
