<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="Theme.AppCompat.Light.NoActionBar.FullScreen" parent="@style/Theme.AppCompat.Light">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="Theme.notAnimation" parent="Theme.AppCompat.Light.NoActionBar.FullScreen">
        <item name="android:windowAnimationStyle">@style/notAnimation</item>
    </style>

    <style name="notAnimation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <style name="input_style">
        <item name="android:layout_marginLeft">3.5dp</item>
        <item name="android:layout_marginRight">3.5dp</item>
        <item name="android:textColor">@color/black_23252B</item>
        <item name="android:layout_width">70dp</item>
        <item name="android:layout_height">70dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">56sp</item>
        <item name="android:cursorVisible">false</item>
        <item name="android:textCursorDrawable">@mipmap/icon_red_line</item>
        <item name="android:background">@drawable/border_grey_20</item>
        <item name="android:textStyle">bold</item>
    </style>

</resources>