<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@mipmap/icon_task_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/moving_content_cl"
        android:layout_width="@dimen/dp_659"
        android:layout_height="@dimen/dp_351"
        android:layout_marginTop="@dimen/dp_203"
        android:layout_marginLeft="@dimen/dp_87"
        android:background="@mipmap/icon_content_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/moving_latest_time_ll"
            android:layout_width="@dimen/dp_368"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_32"
            android:layout_marginRight="@dimen/dp_40"
            android:background="@drawable/bg_time_radius_21"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/icon_latest_time"
                android:layout_marginLeft="@dimen/dp_25"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="最晚结束:"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/orange_FF7700"
                android:layout_marginLeft="@dimen/dp_10"/>

            <TextView
                android:id="@+id/moving_latest_time_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2025-01-12 00:01:00.000"
                android:textSize="@dimen/sp_24"
                android:lines="1"
                android:textColor="@color/orange_FF7700"
                android:layout_marginLeft="@dimen/dp_15"/>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/pick_moving_cl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_111"
            android:layout_marginLeft="@dimen/dp_40"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <ImageView
                android:id="@+id/pick_moving_img"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:src="@mipmap/icon_content_location"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/pick_moving_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_robot_going"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/black_525765"
                android:layout_marginLeft="@dimen/dp_32"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@id/pick_moving_img"/>

            <TextView
                android:id="@+id/moving_stop_type_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_48"
                android:text=""
                android:layout_marginTop="@dimen/dp_12"
                android:layout_marginLeft="@dimen/dp_32"
                app:layout_constraintTop_toBottomOf="@id/pick_moving_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_moving_img"/>

            <TextView
                android:id="@+id/moving_stop_tv"
                android:layout_width="@dimen/dp_350"
                android:layout_height="wrap_content"
                android:textColor="@color/blue_3C6EF0"
                android:textSize="@dimen/sp_56"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginLeft="@dimen/dp_8"
                android:lines="1"
                android:ellipsize="end"
                app:layout_constraintTop_toBottomOf="@id/pick_moving_tv"
                app:layout_constraintLeft_toRightOf="@id/moving_stop_type_tv"/>

            <TextView
                android:id="@+id/moving_serial_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_vehicle_serial"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_24"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dp_32"
                app:layout_constraintTop_toBottomOf="@id/moving_stop_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_moving_img"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/moving_ob_flow_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_pick_owhs"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp_147"
        android:layout_marginRight="@dimen/dp_140"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/moving_ob_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_owhs"
        android:layout_marginTop="@dimen/dp_335"
        android:layout_marginRight="@dimen/dp_155"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/moving_as_arrive_btn"
        android:layout_width="@dimen/dp_348"
        android:layout_height="@dimen/dp_96"
        android:text="@string/string_as_arrive"
        android:textColor="@color/color_white"
        android:textSize="@dimen/sp_42"
        android:background="@drawable/bg_confirm_blue"
        android:layout_marginBottom="@dimen/dp_50"
        android:shadowColor="@color/blue_521944B3"
        android:shadowDx="0"
        android:shadowDy="2"
        android:textStyle="bold"
        android:letterSpacing="1.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>