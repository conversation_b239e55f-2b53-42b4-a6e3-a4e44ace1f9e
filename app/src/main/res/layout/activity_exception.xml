<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@mipmap/icon_exception_bg">

    <LinearLayout
        android:id="@+id/exc_back_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_74"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_back"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_back"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/black_525765"
            android:layout_marginLeft="@dimen/dp_8"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_80"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_exception"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/string_vehicle_exception"
            android:textSize="@dimen/sp_32"
            android:textColor="@color/black_23252B"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/dp_16"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/exc_item_rv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_160"
        android:layout_marginBottom="@dimen/dp_120"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/exc_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_72"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@mipmap/icon_exception_bottom">
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/exc_call_person_tv"
        android:layout_width="@dimen/dp_348"
        android:layout_height="@dimen/dp_96"
        android:text="@string/string_call_person"
        android:textSize="@dimen/sp_42"
        android:textColor="@color/color_white"
        android:textStyle="bold"
        android:background="@drawable/bg_confirm_blue"
        android:layout_marginBottom="@dimen/dp_40"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>