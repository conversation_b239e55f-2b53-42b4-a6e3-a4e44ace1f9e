<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_760"
    android:layout_height="@dimen/dp_400"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_robot_qr">

    <LinearLayout
        android:id="@+id/dialog_call_close_ll"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:gravity="center"
        android:layout_marginTop="@dimen/dp_22"
        android:layout_marginRight="@dimen/dp_22"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <ImageView
            android:id="@+id/dialog_call_close_img"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@mipmap/icon_close"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/dialog_call_success_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_call_success"
        android:layout_marginTop="@dimen/dp_75"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/dialog_call_success_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="呼叫成功"
        android:textSize="@dimen/sp_32"
        android:textColor="@color/black_23252B"
        android:layout_marginTop="@dimen/dp_24"
        app:layout_constraintTop_toBottomOf="@id/dialog_call_success_img"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/dialog_call_content_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/string_call_notice"
        android:textColor="@color/black_525765"
        android:textSize="@dimen/sp_24"
        android:layout_marginTop="@dimen/dp_16"
        app:layout_constraintTop_toBottomOf="@id/dialog_call_success_tv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/dialog_call_know_tv"
        android:layout_width="@dimen/dp_336"
        android:layout_height="@dimen/dp_80"
        android:text="我知道啦"
        android:textSize="@dimen/sp_24"
        android:textColor="@color/color_white"
        android:background="@drawable/bg_confirm_blue"
        android:layout_marginTop="@dimen/dp_32"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/dialog_call_content_tv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>