<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_100"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/item_goods_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_exception"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/item_goods_img"
        android:orientation="vertical">
        <TextView
            android:id="@+id/item_goods_serial_no_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上装号："/>

        <TextView
            android:id="@+id/item_goods_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="商品名称："
            android:lines="2"
            android:ellipsize="end"/>
        <TextView
            android:id="@+id/item_goods_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="商品件数："/>
        <TextView
            android:id="@+id/item_goods_grid_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="所在格口："/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>