<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/color_white">

    <Button
        android:id="@+id/btn_connect"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="连接"/>

    <Button
        android:id="@+id/btn_get_car_info"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="获取车辆信息"/>

    <Button
        android:id="@+id/btn_get_battery_info"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="获取电量信息"/>

    <Button
        android:id="@+id/btn_get_location_info"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="获取定位信息"/>
    <Button
        android:id="@+id/btn_get_mqtt_info"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="获取MQTT信息"/>

    <Button
        android:id="@+id/btn_get_task_info"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="获取任务信息"/>

    <Button
        android:id="@+id/btn_as_arrived"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="视同到达"/>

    <Button
        android:id="@+id/btn_up_robot"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="上升立柱"/>

    <Button
        android:id="@+id/btn_down_robot"
        android:layout_width="@dimen/dp_200"
        android:layout_height="wrap_content"
        android:text="下降立柱"/>

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="上报的信息"/>

</LinearLayout>