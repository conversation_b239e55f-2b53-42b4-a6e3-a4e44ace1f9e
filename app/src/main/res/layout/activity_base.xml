<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/base_whole_cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/base_head_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:elevation="@dimen/dp_5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">

        <TextView
            android:id="@+id/base_time_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_24"
            android:text="下午20:00"
            android:textColor="@color/black_525765"
            android:textSize="@dimen/sp_20"
            android:layout_marginTop="1@dimen/dp_6"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <TextView
            android:id="@+id/base_date_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_24"
            android:text="4月8号周二"
            android:textColor="@color/black_525765"
            android:textSize="@dimen/sp_20"
            android:layout_marginTop="1@dimen/dp_6"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toRightOf="@id/base_time_tv"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginTop="1@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_24"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <ImageView
                android:id="@+id/base_mqtt_state_img"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_3"
                android:src="@mipmap/icon_mqtt_disconnect" />

            <TextView
                android:id="@+id/base_head_mqtt_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_mqtt_disconnect"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_20"/>

            <ImageView
                android:id="@+id/base_head_location_img"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_3"
                android:layout_marginLeft="@dimen/dp_20"
                android:src="@mipmap/icon_no_loc"/>

            <TextView
                android:id="@+id/base_head_loc_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_not_location_tip"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_20"/>

            <TextView
                android:id="@+id/base_battery_value_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100%"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_20"
                android:layout_marginLeft="@dimen/dp_20"/>

            <ImageView
                android:id="@+id/base_battery_view"
                android:layout_width="2@dimen/dp_2"
                android:layout_height="2@dimen/dp_2"
                android:src="@mipmap/icon_battery_100"
                android:layout_marginTop="@dimen/dp_5"/>





        </LinearLayout>

<!--        <ImageView-->
<!--            android:id="@+id/base_battery_view"-->
<!--            android:layout_width="2@dimen/dp_2"-->
<!--            android:layout_height="2@dimen/dp_2"-->
<!--            android:src="@mipmap/icon_battery_100"-->
<!--            android:layout_marginTop="@dimen/dp_20"-->
<!--            android:layout_marginRight="@dimen/dp_24"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toRightOf="parent"/>-->

<!--        <TextView-->
<!--            android:id="@+id/base_battery_value_tv"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_centerVertical="true"-->
<!--            android:layout_marginTop="@dimen/dp_14"-->
<!--            android:layout_marginRight="@dimen/dp_2"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toLeftOf="@id/base_battery_view"-->
<!--            android:text="100%"-->
<!--            android:textColor="@color/black_525765"-->
<!--            android:textSize="@dimen/sp_20" />-->

<!--        <TextView-->
<!--            android:id="@+id/base_head_loc_tv"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="@string/string_not_location_tip"-->
<!--            android:textColor="@color/black_525765"-->
<!--            android:textSize="@dimen/sp_20"-->
<!--            android:layout_marginRight="@dimen/dp_20"-->
<!--            android:layout_marginTop="1@dimen/dp_6"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toLeftOf="@id/base_battery_value_tv"/>-->


<!--        <ImageView-->
<!--            android:id="@+id/base_head_location_img"-->
<!--            android:layout_width="@dimen/dp_24"-->
<!--            android:layout_height="@dimen/dp_24"-->
<!--            android:src="@mipmap/icon_no_loc"-->
<!--            android:layout_marginRight="1@dimen/dp_8"-->
<!--            android:layout_marginTop="1@dimen/dp_6"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toLeftOf="@id/base_head_loc_tv"/>-->

<!--        <TextView-->
<!--            android:id="@+id/base_head_mqtt_tv"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="@string/string_mqtt_disconnect"-->
<!--            android:textColor="@color/black_525765"-->
<!--            android:textSize="@dimen/sp_20"-->
<!--            android:layout_marginRight="@dimen/dp_20"-->
<!--            android:layout_marginTop="1@dimen/dp_6"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toLeftOf="@id/base_head_location_img"/>-->

<!--        <ImageView-->
<!--            android:id="@+id/base_mqtt_state_img"-->
<!--            android:layout_width="@dimen/dp_24"-->
<!--            android:layout_height="@dimen/dp_24"-->
<!--            android:src="@mipmap/icon_mqtt_disconnect"-->
<!--            android:layout_marginRight="1@dimen/dp_8"-->
<!--            android:layout_marginTop="1@dimen/dp_6"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toLeftOf="@id/base_head_mqtt_tv"/>-->


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/base_content_cl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <com.jd.amr.hmi.ui.widget.DraggableButton
        android:id="@+id/base_exception_btn"
        android:layout_width="7@dimen/dp_2"
        android:layout_height="7@dimen/dp_2"
        android:layout_marginRight="2@dimen/dp_6"
        android:layout_marginBottom="14@dimen/dp_5"
        android:visibility="gone"
        android:gravity="center"
        android:background="@drawable/bg_exception_icon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_exception_notice"/>

    </com.jd.amr.hmi.ui.widget.DraggableButton>



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/base_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="7@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@mipmap/icon_home_bottom_bg">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginLeft="3@dimen/dp_2"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">
            <ImageView
                android:id="@+id/base_bottom_location_img"
                android:layout_width="2@dimen/dp_5"
                android:layout_height="2@dimen/dp_5"
                android:src="@mipmap/icon_location"/>

            <TextView
                android:id="@+id/base_location_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/dp_5"
                android:text="未定位"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_22" />
            <TextView
                android:id="@+id/base_location_name_tv"
                android:layout_width="@dimen/dp_261"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_22"
                android:textColor="@color/black_525765"
                android:lines="1"
                android:ellipsize="end"
                android:layout_marginLeft="@dimen/dp_10"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/base_robot_img"
        android:layout_width="7@dimen/dp_3"
        android:layout_height="@dimen/dp_107"
        android:src="@mipmap/icon_robot_bottom_bg"
        android:elevation="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_31"
        android:layout_marginBottom="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <LinearLayout
        android:id="@+id/base_robot_name_ll"
        android:layout_width="18@dimen/dp_6"
        android:layout_height="@dimen/dp_44"
        android:background="@drawable/bg_robot_radius_20"
        android:gravity="center"
        android:elevation="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_40"
        android:layout_marginBottom="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        >
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_robot_name_bg"
            android:layout_marginLeft="@dimen/dp_3"
            android:layout_marginTop="@dimen/dp_2"/>
        <TextView
            android:id="@+id/base_robot_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="@dimen/sp_22"
            android:textColor="@color/color_white"
            android:letterSpacing="0.2"
            android:layout_marginLeft="@dimen/dp_5"/>
        <ImageView
            android:layout_width="@dimen/dp_17"
            android:layout_height="@dimen/dp_17"
            android:src="@mipmap/icon_arrow"
            android:layout_marginRight="@dimen/dp_2"
            android:layout_marginTop="@dimen/dp_2"/>
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_22"
        android:textColor="@color/black_525765"
        android:text="@string/string_robot_name"
        android:layout_marginLeft="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/base_robot_name_ll"/>

</androidx.constraintlayout.widget.ConstraintLayout>