<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_grid_cl"
    android:layout_width="@dimen/dp_60"
    android:layout_height="@dimen/dp_50"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/item_grid_stop_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="停靠点"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/item_grid_no_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="10"
        app:layout_constraintTop_toBottomOf="@id/item_grid_stop_tv"
        app:layout_constraintLeft_toLeftOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>