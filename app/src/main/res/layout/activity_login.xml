<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activity.LoginActivity">

    <TextView
        android:id="@+id/login_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/string_login_title"
        android:textSize="58sp"
        android:layout_marginTop="80dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <LinearLayout
        android:id="@+id/login_code_cl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_title_tv">

        <com.jd.amr.hmi.ui.widget.CodeEditText
            android:id="@+id/code_edit_text1"
            style="@style/input_style"
            android:background="@drawable/bg_border_red_radius_20" />

        <com.jd.amr.hmi.ui.widget.CodeEditText
            android:id="@+id/code_edit_text2"
            style="@style/input_style" />

        <com.jd.amr.hmi.ui.widget.CodeEditText
            android:id="@+id/code_edit_text3"
            style="@style/input_style" />

        <com.jd.amr.hmi.ui.widget.CodeEditText
            android:id="@+id/code_edit_text4"
            style="@style/input_style" />

    </LinearLayout>


    <com.jd.amr.hmi.ui.widget.VerifyNumberKeyboardView
        android:id="@+id/login_keyboard_view"
        android:layout_width="582dp"
        android:layout_height="380dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.7"
        app:verifyNumberDeleteNormalSrc="@mipmap/ic_number_keyboard_delete_normal"
        app:verifyNumberDeletePressedSrc="@mipmap/ic_number_keyboard_delete_pressed"
        app:verifyNumberNormalSrc="@mipmap/bg_number_keyboard_normal"
        app:verifyNumberPressedSrc="@mipmap/bg_number_keyboard_pressed"
        app:verifyNumberResetNormalSrc="@mipmap/ic_number_keyboard_reset_normal"
        app:verifyNumberResetPressedSrc="@mipmap/ic_number_keyboard_reset_pressed" />


</androidx.constraintlayout.widget.ConstraintLayout>