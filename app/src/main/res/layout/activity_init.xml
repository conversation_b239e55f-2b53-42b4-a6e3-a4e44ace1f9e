<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/init_whole_cl"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/icon_home_bg"
    tools:context=".ui.activity.InitActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/init_content_cl"
        android:layout_width="61@dimen/dp_6"
        android:layout_height="1@dimen/dp_24"
        android:background="@mipmap/icon_home_content_bg"
        android:layout_marginTop="5@dimen/dp_10"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <TextView
            android:id="@+id/init_content_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="欢迎使用京东仓储AMR"
            android:textSize="@dimen/sp_38"
            android:textColor="@color/black_23252B"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_home_content_shadow_bg"
        app:layout_constraintTop_toBottomOf="@id/init_content_cl"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>