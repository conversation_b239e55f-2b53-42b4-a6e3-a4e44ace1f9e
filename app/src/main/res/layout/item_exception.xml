<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_144"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_exception_item">

    <TextView
        android:id="@+id/item_exception_sort_tv"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:background="@drawable/bg_exception_sort"
        android:text="1"
        android:textColor="@color/color_white"
        android:textSize="@dimen/sp_24"
        android:gravity="center"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginLeft="@dimen/dp_24"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/item_exception_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="异常名称"
        android:textColor="@color/black_23252B"
        android:textSize="@dimen/sp_32"
        android:layout_marginLeft="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_24"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/item_exception_sort_tv"/>

    <TextView
        android:id="@+id/item_exception_solution_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="解决办法"
        android:textColor="@color/black_525765"
        android:textSize="@dimen/sp_24"
        android:lines="1"
        android:layout_marginLeft="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintTop_toBottomOf="@id/item_exception_name_tv"
        app:layout_constraintLeft_toRightOf="@id/item_exception_sort_tv"/>


</androidx.constraintlayout.widget.ConstraintLayout>