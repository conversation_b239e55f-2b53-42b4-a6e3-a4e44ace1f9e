<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_787"
    android:layout_height="@dimen/dp_345"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_robot_qr">

    <ImageView
        android:id="@+id/robot_qr_close_img"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:src="@mipmap/icon_close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/dp_25"
        android:layout_marginRight="@dimen/dp_30"/>

    <TextView
        android:id="@+id/robot_qr_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="机器人编号："
        android:textSize="@dimen/sp_32"
        android:textColor="@color/black_23252B"
        android:layout_marginTop="@dimen/dp_64"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>
    
    <ImageView
        android:id="@+id/robot_qr_code_img"
        android:layout_width="@dimen/dp_573"
        android:layout_height="@dimen/dp_157"
        android:layout_marginTop="@dimen/dp_21"
        app:layout_constraintTop_toBottomOf="@id/robot_qr_name_tv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>