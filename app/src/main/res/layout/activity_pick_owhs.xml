<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/icon_task_bg"
    tools:ignore="MissingDefaultResource">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/pick_content_cl"
        android:layout_width="@dimen/dp_659"
        android:layout_height="@dimen/dp_351"
        android:layout_marginTop="21@dimen/dp_6"
        android:layout_marginLeft="@dimen/dp_64"
        android:background="@mipmap/icon_content_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="36@dimen/dp_8"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:layout_marginTop="3@dimen/dp_2"
            android:layout_marginRight="@dimen/dp_40"
            android:background="@drawable/bg_time_radius_21"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/icon_latest_time"
                android:layout_marginLeft="2@dimen/dp_5"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="最晚结束:"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/orange_FF7700"
                android:layout_marginLeft="@dimen/dp_10"/>

            <TextView
                android:id="@+id/pick_latest_time_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="06月12日 23:59"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/orange_FF7700"
                android:lines="1"
                android:layout_marginLeft="1@dimen/dp_5"/>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/pick_wait_bind_cl"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_200"
            android:visibility="gone"
            android:layout_marginLeft="@dimen/dp_40"
            android:layout_marginTop="8@dimen/dp_8"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">
            <ImageView
                android:id="@+id/pick_lock_img"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:layout_marginTop="1@dimen/dp_6"
                android:src="@mipmap/icon_lock"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>

            <TextView
                android:id="@+id/pick_wait_bind_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请绑定"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_20"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@id/pick_lock_img"/>

            <TextView
                android:id="@+id/pick_wait_bind_no_tv"
                android:layout_width="43@dimen/dp_3"
                android:layout_height="wrap_content"
                android:text="边拣边分12格口上装"
                android:textColor="@color/black_23252B"
                android:textSize="@dimen/sp_48"
                android:layout_marginLeft="3@dimen/dp_2"
                android:layout_marginTop="@dimen/dp_5"
                android:lines="1"
                android:ellipsize="end"
                app:layout_constraintTop_toBottomOf="@id/pick_wait_bind_title_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_lock_img"/>

            <TextView
                android:id="@+id/pick_wait_bind_serial_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_scan_vehicle_serial_title"
                android:textSize="@dimen/sp_20"
                android:textColor="@color/black_525765"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toBottomOf="@id/pick_wait_bind_no_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_lock_img"/>

            <LinearLayout
                android:id="@+id/pick_wait_scan_serial_ll"
                android:layout_width="@dimen/dp_400"
                android:layout_height="@dimen/dp_50"
                android:gravity="center_vertical"
                android:layout_marginLeft="3@dimen/dp_2"
                android:background="@drawable/bg_scan_serial_num"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/pick_lock_img">

                <EditText
                    android:id="@+id/pick_wait_scan_serial_et"
                    android:layout_width="3@dimen/dp_50"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:textColor="@color/black_525765"
                    android:textSize="@dimen/sp_20"
                    android:lines="1"
                    android:background="@null"
                    android:hint="请扫描上装号"
                    android:textColorHint="@color/black_BABEC7"/>

                <ImageView
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginLeft="@dimen/dp_5"
                    android:src="@mipmap/icon_scan"/>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/pick_pick_cl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginLeft="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_111"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">
            <ImageView
                android:id="@+id/pick_pick_img"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:src="@mipmap/icon_lock"
                android:layout_marginTop="@dimen/dp_20"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>

            <TextView
                android:id="@+id/pick_pick_arrived_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_robot_arrived"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_24"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@id/pick_pick_img"/>

            <TextView
                android:id="@+id/pick_pick_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_load_title"
                android:textStyle="bold"
                android:textColor="@color/black_23252B"
                android:textSize="@dimen/sp_48"
                android:layout_marginTop="1@dimen/dp_2"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toBottomOf="@id/pick_pick_arrived_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_pick_img"/>

            <TextView
                android:id="@+id/pick_pick_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_vehicle_serial"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/black_525765"
                android:layout_marginTop="1@dimen/dp_2"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toBottomOf="@id/pick_pick_title_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_pick_img"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/pick_unload_cl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginLeft="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_111"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <ImageView
                android:id="@+id/pick_unload_lock_img"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:layout_marginTop="@dimen/dp_20"
                android:src="@mipmap/icon_lock"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"/>

            <TextView
                android:id="@+id/pick_unload_arrived_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_robot_arrived"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_24"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toRightOf="@id/pick_unload_lock_img"/>

            <TextView
                android:id="@+id/pick_unload_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_unload_title"
                android:textColor="@color/black_525765"
                android:textSize="@dimen/sp_48"
                android:textStyle="bold"
                android:layout_marginTop="1@dimen/dp_2"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toBottomOf="@id/pick_unload_arrived_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_unload_lock_img"/>

            <TextView
                android:id="@+id/pick_unload_sn_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_vehicle_serial"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/black_525765"
                android:layout_marginTop="1@dimen/dp_2"
                android:layout_marginLeft="3@dimen/dp_2"
                app:layout_constraintTop_toBottomOf="@id/pick_unload_title_tv"
                app:layout_constraintLeft_toRightOf="@id/pick_unload_lock_img"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_content_shadow_bg"
        android:layout_marginLeft="4@dimen/dp_8"
        app:layout_constraintTop_toBottomOf="@id/pick_content_cl"
        app:layout_constraintLeft_toLeftOf="parent" />

    <ImageView
        android:id="@+id/pick_pick_owhs_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_pick_owhs"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp_147"
        android:layout_marginRight="1@dimen/dp_40"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/pick_owhs_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_owhs"
        android:layout_marginTop="33@dimen/dp_5"
        android:layout_marginRight="15@dimen/dp_5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/pick_up_down_line_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/icon_up_down_line"
        android:layout_marginTop="@dimen/dp_370"
        android:layout_marginRight="12@dimen/dp_2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <LinearLayout
        android:id="@+id/pick_robot_up_down_ll"
        android:layout_width="@dimen/dp_80"
        android:layout_height="2@dimen/dp_24"
        android:background="@drawable/bg_robot_up_down"
        android:orientation="vertical"
        android:layout_marginTop="3@dimen/dp_50"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/pick_up_down_line_img">
        <LinearLayout
            android:id="@+id/pick_robot_up_ll"
            android:layout_width="7@dimen/dp_2"
            android:layout_height="10@dimen/dp_8"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_margin="@dimen/dp_3"
            android:background="@drawable/bg_robot_upright"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent">
            <ImageView
                android:id="@+id/pick_robot_up_img"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:src="@mipmap/icon_robot_up_white" />
            <TextView
                android:id="@+id/pick_robot_up_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_up_robot"
                android:textColor="@color/color_white"
                android:textSize="@dimen/sp_24"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/pick_robot_down_ll"
            android:layout_width="7@dimen/dp_2"
            android:layout_height="10@dimen/dp_8"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_margin="@dimen/dp_3"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent">
            <TextView
                android:id="@+id/pick_robot_down_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_down_robot"
                android:textSize="@dimen/sp_24"
                android:textColor="@color/black_525765"/>
            <ImageView
                android:id="@+id/pick_robot_down_img"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:src="@mipmap/icon_robot_down_black"/>
        </LinearLayout>


    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/pick_scan_confirm_btn"
        android:layout_width="34@dimen/dp_8"
        android:layout_height="9@dimen/dp_6"
        android:visibility="gone"
        android:text="@string/string_confirm"
        android:textColor="@color/color_white"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        android:letterSpacing="1.5"
        android:background="@drawable/bg_confirm_blue"
        android:layout_marginBottom="@dimen/dp_50"
        android:shadowColor="@color/blue_521944B3"
        android:shadowDx="0"
        android:shadowDy="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>