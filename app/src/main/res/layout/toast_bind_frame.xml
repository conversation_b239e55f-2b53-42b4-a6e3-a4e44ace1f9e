<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_617"
    android:layout_height="@dimen/dp_144"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_robot_qr">

    <ImageView
        android:id="@+id/toast_error_img"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:src="@mipmap/icon_red_error"
        android:layout_marginLeft="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_32"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="@dimen/dp_24"
        android:layout_marginRight="@dimen/dp_56"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@id/toast_error_img">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上装绑定错误"
            android:textColor="@color/black_23252B"
            android:textStyle="bold"
            android:textSize="@dimen/sp_32"/>
        <TextView
            android:id="@+id/toast_error_msg_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请确认"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/black_525765"
            android:layout_marginTop="@dimen/dp_12"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>