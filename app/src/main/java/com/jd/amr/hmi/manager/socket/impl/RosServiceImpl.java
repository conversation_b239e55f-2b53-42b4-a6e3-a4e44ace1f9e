package com.jd.amr.hmi.manager.socket.impl;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.iscas.rcljava.entity.service.ArrivedResp;
import com.iscas.rcljava.entity.service.CommonResp;
import com.iscas.rcljava.entity.service.TowingResult;
import com.iscas.rcljava.entity.topic.BatteryState;
import com.iscas.rcljava.entity.topic.LocalizationState;
import com.iscas.rcljava.entity.topic.MqttState;
import com.iscas.rcljava.entity.topic.RobotMonitorState;
import com.iscas.rcljava.entity.topic.TaskInfo;
import com.iscas.rcljava.entity.topic.VehicleInfo;
import com.iscas.rcljava.robot.AbsMessageListener;
import com.iscas.rcljava.robot.OpenRos2Interface;
import com.iscas.rcljava.socket.ConnectionStatusListener;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.socket.IRosService;
import com.jd.amr.hmi.util.NetworkUtils;
import com.jd.ugv.pad.common.utils.LogUtils;

import java.net.InetSocketAddress;

/**
 * 实现类
 */
public class RosServiceImpl implements IRosService {

    private static final String TAG = "RosServiceImpl";


    @Override
    public void connect(Context context, ConnectionStatusListener listener) {
        String ip = NetworkUtils.getIPAddress(context);
        int port = (int)(Math.random() * 10000 + 10000);
        if(TextUtils.isEmpty(ip)){
            ip = Constants.androidIp;
        }
        LogUtils.i(TAG, "当前ip: " + ip + ", port: " + port + ", 连接的机器人地址：" + Constants.rosBridgeUri);
        OpenRos2Interface.INSTANCE.init(listener, new InetSocketAddress(ip, port), Constants.rosBridgeUri);
    }

    @Override
    public void disconnect() {
        OpenRos2Interface.INSTANCE.disconnect();
    }

    @Override
    public void registerVehicleInfo(AbsMessageListener<VehicleInfo> listener) {
        OpenRos2Interface.INSTANCE.subscribeVehicleInfo(listener);
    }

    @Override
    public void registerMqttState(AbsMessageListener<MqttState> listener) {
        OpenRos2Interface.INSTANCE.subscribeMqttState(listener);
    }

    @Override
    public void unregisterMqttState() {
        OpenRos2Interface.INSTANCE.unsubscribeMqttState(null);
    }

    @Override
    public void unregisterVehicleInfo() {
        OpenRos2Interface.INSTANCE.unsubscribeVehicleInfo(null);
    }

    @Override
    public void registerBattery(AbsMessageListener<BatteryState> listener) {
        OpenRos2Interface.INSTANCE.subscribeBatteryState(listener);
    }

    @Override
    public void unregisterBattery() {
        OpenRos2Interface.INSTANCE.unsubscribeBatteryState(null);
    }

    @Override
    public void registerLocation(AbsMessageListener<RobotMonitorState> listener) {
        OpenRos2Interface.INSTANCE.subscribeMotionState(listener);
    }

    @Override
    public void unregisterLocation() {
        OpenRos2Interface.INSTANCE.unsubscribeMotionState(null);
    }

    @Override
    public void registerTaskInfo(AbsMessageListener<TaskInfo> listener) {
        OpenRos2Interface.INSTANCE.subscribeTaskInfo(listener);
    }

    @Override
    public void unregisterTaskInfo() {
        OpenRos2Interface.INSTANCE.unsubscribeTaskInfo(null);
    }

    @Override
    public void robotAsArrived(String taskId, AbsMessageListener<ArrivedResp> listener) {
        OpenRos2Interface.INSTANCE.callRobotAsArrive(taskId, listener);
    }

    @Override
    public void robotTowingAction(int type, AbsMessageListener<TowingResult> listener) {
        OpenRos2Interface.INSTANCE.callTowingAction(type, listener);
    }

    @Override
    public void robotClickButton() {
        OpenRos2Interface.INSTANCE.callClickButton();
    }
}
