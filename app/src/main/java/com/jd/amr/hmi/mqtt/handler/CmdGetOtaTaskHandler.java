package com.jd.amr.hmi.mqtt.handler;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.amr.hmi.Amr;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.model.data.entity.DeviceVersionInfo;
import com.jd.amr.hmi.model.data.entity.OtaTaskStatusInfo;
import com.jd.amr.hmi.model.data.entity.VersionInfoDetail;
import com.jd.amr.hmi.model.data.mqtt.MqttResponseData;
import com.jd.amr.hmi.model.enums.OtaTaskStatus;
import com.jd.amr.hmi.model.enums.RemoteCmdType;
import com.jd.amr.hmi.mqtt.JdMqttService;
import com.jd.amr.hmi.util.VersionUtil;
import com.jd.ugv.pad.common.utils.LogUtils;

import java.lang.reflect.Type;
import java.util.List;

/**
 * 获取设备升级任务
 */
public class CmdGetOtaTaskHandler implements ICmdHandler{

    private static final String TAG = "CmdGetOtaTaskHandler";
    @Override
    public void handleMsg(Context context, String msg) {
        Type type = new TypeToken<MqttResponseData<DeviceVersionInfo>>() {
        }.getType();
        Gson gson = new Gson();
        MqttResponseData<DeviceVersionInfo> data = gson.fromJson(msg, type);
        if (data == null || data.getData() == null) {
            LogUtils.i(TAG, RemoteCmdType.CMD_GET_OTA_TASK.name() + " data is null");
        }else {
            handleOtaTaskData(data.getData());
        }
    }

    private void handleOtaTaskData(DeviceVersionInfo data) {
        LogUtils.i(TAG, "handleOtaTaskData===>data: " + data.toString());
        List<VersionInfoDetail> versionInfoList = data.getVersionInfoList();
        if(versionInfoList != null && !versionInfoList.isEmpty()){
            VersionInfoDetail versionInfoDetail = null;
            for(VersionInfoDetail info : versionInfoList) {
                if (TextUtils.equals(info.getAppName(), Constants.APP_NAME)) {
                    versionInfoDetail = info;
                    break;
                }
            }
            OtaTaskStatusInfo otaTaskStatusInfo = getOtaTaskStatusInfo(data.getProductKey(), data.getDeviceName(), versionInfoDetail.getIssueTaskNumber(), versionInfoDetail.getVersion());
            OtaTaskStatusInfo oldOtaTaskStatusInfo = GlobalRobotDataManager.getInstance().getOtaTaskStatusInfo();
            if(versionInfoDetail == null){
                LogUtils.i(TAG, "handleOtaTaskData===>versionInfoDetail is null");
                return;
            }
            if(oldOtaTaskStatusInfo != null && oldOtaTaskStatusInfo.getIssueTaskNumber().equals(otaTaskStatusInfo.getIssueTaskNumber())){
                LogUtils.i(TAG, "handleOtaTaskData===>相同的升级任务，不处理");
                return;
            }
            GlobalRobotDataManager.getInstance().updateOtaTaskStatus(otaTaskStatusInfo, OtaTaskStatus.RECEIVED.getCode(), OtaTaskStatus.RECEIVED.getDescription());
            String url = versionInfoDetail.getUrl();
            if(TextUtils.isEmpty(url)) {
                LogUtils.i(TAG, "handleOtaTaskData===>url is null");
                return;
            }
            String versionStr = versionInfoDetail.getVersion();
            if(TextUtils.isEmpty(versionStr)) {
                LogUtils.i(TAG, "handleOtaTaskData===>version is null");
                return;
            }
            try{
                int curVerCode = VersionUtil.getInstance().getVersionCode();
                LogUtils.i(TAG, "handleOtaTaskData===>curVerCode: " + curVerCode + " updateVerCode: " + versionStr);
                if(Integer.parseInt(versionStr) <= curVerCode){
                    return;
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            String name = VersionUtil.getInstance().getPkgName() + "_" + versionStr;
            VersionUtil.getInstance().downLoadApkFile(url, name, 0, 0, versionInfoDetail.getIssueTaskNumber(), versionStr, otaTaskStatusInfo);
        }
    }

    private OtaTaskStatusInfo getOtaTaskStatusInfo(String productKey, String deviceName, String taskNum, String versionStr) {
        OtaTaskStatusInfo otaTaskStatusInfo = new OtaTaskStatusInfo();
        otaTaskStatusInfo.setIssueTaskNumber(taskNum);
        otaTaskStatusInfo.setVersion(versionStr);
        otaTaskStatusInfo.setProductKey(productKey);
        otaTaskStatusInfo.setDeviceName(deviceName);
        otaTaskStatusInfo.setAppName(Constants.APP_NAME);
        return otaTaskStatusInfo;
    }

    @Override
    public String cmdType() {
        return RemoteCmdType.CMD_GET_OTA_TASK.getType();
    }
}
