package com.jd.amr.hmi.ui.activity;

import android.app.ActionBar;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.gson.Gson;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.databinding.ActivityBaseBinding;

import com.jd.amr.hmi.R;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.service.RobotRemoteService;
import com.jd.amr.hmi.model.data.event.AlarmDetailInfo;
import com.jd.amr.hmi.model.data.event.AlarmInfoEvent;
import com.jd.amr.hmi.model.data.event.MonitorMsgEvent;
import com.jd.amr.hmi.model.data.event.MqttMsgEvent;
import com.jd.amr.hmi.model.enums.BusinessType;
import com.jd.amr.hmi.ui.dialog.RobotDialog;
import com.jd.amr.hmi.ui.widget.DraggableButton;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.StringUtils;
import com.jdx.rover.transport.integrate.ReportTask;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public abstract class BaseActivity extends AppCompatActivity {

    protected final String TAG = this.getClass().getSimpleName();
    private ActivityBaseBinding binding;

    private TextView mBaseTimeTv;
    private TextView mBaseDateTv;
//    private BatteryView mBatteryView;
    private ImageView mBaseBatteryImg;
    private TextView mBatteryValueTv;
    private TextView mBaseHeadLocTv;
    private ImageView mBaseHeadLocImg;
    private TextView mMqttStateTv;
    private ImageView mMqttStateImg;
    public DraggableButton mBaseDraBtn;

    public ConstraintLayout mBaseBottomConLl;
    public ImageView mBaseBottomLocImg;
    public TextView mBaseBottomStopTitleTv;
    public TextView mBaseBottomStopNameTv;
    public LinearLayout mRobotNameLl;
    public TextView mRobotNameTv;
    public RobotDialog mRobotDialog;

    private Timer mTimer;

    private TimerTask mTimerTask;
    public Intent mRobotRemoteService;

    public Intent mJdMqttService;

    private static final int MSG_UPDATE_TIME = 1;

    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_UPDATE_TIME:
                    updateCurrentDateTime();
                    break;
            }
        }
    };


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LogUtils.i(TAG, "=========onCreate=========");
        binding = ActivityBaseBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        ViewGroup contentView = findViewById(R.id.base_content_cl);
        LayoutInflater inflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View childView = inflater.inflate(layoutId(), null);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ActionBar.LayoutParams.MATCH_PARENT);
        contentView.addView(childView, params);

        mBaseDraBtn = findViewById(R.id.base_exception_btn);
        mBaseTimeTv = findViewById(R.id.base_time_tv);
        mBaseDateTv = findViewById(R.id.base_date_tv);
        mBaseBatteryImg = findViewById(R.id.base_battery_view);
        mBatteryValueTv = findViewById(R.id.base_battery_value_tv);
        mBaseHeadLocTv = findViewById(R.id.base_head_loc_tv);
        mBaseHeadLocImg = findViewById(R.id.base_head_location_img);
        mMqttStateTv = findViewById(R.id.base_head_mqtt_tv);
        mMqttStateImg = findViewById(R.id.base_mqtt_state_img);
        mBaseBottomConLl = findViewById(R.id.base_bottom_container);
        mBaseBottomLocImg = findViewById(R.id.base_bottom_location_img);
        mBaseBottomStopTitleTv = findViewById(R.id.base_location_title_tv);
        mBaseBottomStopNameTv = findViewById(R.id.base_location_name_tv);
        mRobotNameLl = findViewById(R.id.base_robot_name_ll);
        mRobotNameTv = findViewById(R.id.base_robot_name_tv);
        initView();

        mRobotNameLl.setOnClickListener(v -> {
            showRobotNameDialog();
        });

        mBaseDraBtn.setOnBtnClickListener(new DraggableButton.OnClickListener() {
            @Override
            public void onClick() {
                LogUtils.i(TAG, "点击悬浮按钮");
                Intent intent = new Intent(getActivity(), ExceptionActivity.class);
                startActivity(intent);
            }
        });
    }

    protected abstract Activity getActivity();

    protected abstract int layoutId();

    protected abstract void initView();

    protected abstract void initListener();

    protected abstract void initData();

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.i(TAG, "=========onResume=========");
        updateCurrentDateTime();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onStart() {
        super.onStart();
        LogUtils.i(TAG, "=========onStart=========");
        startTime();
        if(!BuildConfig.DEBUG) {
            startRemoteService();
        }
        initBaseView();
        initData();
        initListener();
    }

    @Override
    protected void onPause() {
        super.onPause();
        LogUtils.i(TAG, "=========onPause=========");
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtils.i(TAG, "=========onDestroy=========");
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        //禁用返回键
        if(event.getKeyCode() == KeyEvent.KEYCODE_BACK){
            return true;
        }
        return super.dispatchKeyEvent(event);
    }


    private void startTime() {
        stopTime();
        mTimer = new Timer();
        mTimerTask = new TimerTask() {
            @Override
            public void run() {
                mHandler.obtainMessage(MSG_UPDATE_TIME).sendToTarget();
            }
        };
        mTimer.scheduleAtFixedRate(mTimerTask, 1000, 1000 * 60);
    }

    private void stopTime(){
        if(mTimer != null) {
             mTimer.cancel();
             mTimer = null;
        }
        if(mTimerTask != null){
            mTimerTask.cancel();
            mTimerTask = null;
        }
    }


    private void startRemoteService(){
        mRobotRemoteService = new Intent(this, RobotRemoteService.class);
        mRobotRemoteService.setAction(RobotRemoteService.ROBOT_REMOTE_INIT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(mRobotRemoteService);
        } else {
            startService(mRobotRemoteService);
        }
    }

    private void updateCurrentDateTime(){
        long time = System.currentTimeMillis();
        mBaseTimeTv.setText(DateUtils.formatDateTime(this, time, DateUtils.FORMAT_SHOW_TIME));
        String week = DateUtils.formatDateTime(this, time, DateUtils.FORMAT_SHOW_WEEKDAY);
        String daily = DateUtils.formatDateTime(this, time, DateUtils.FORMAT_SHOW_DATE);
        mBaseDateTv.setText(daily + week);
    }

    @Subscribe
    public void onMqttEvent(MqttMsgEvent event) {
        if(event != null){
            LogUtils.i(TAG, "onMqttEvent===>event: " + event.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mMqttStateImg.setImageResource(event.isConnected() ? R.mipmap.icon_mqtt_connect : R.mipmap.icon_mqtt_disconnect);
                    mMqttStateTv.setVisibility(event.isConnected() ? View.GONE : View.VISIBLE);
                }
            });
        }
    }

    @Subscribe
    public void onMonitorEvent(MonitorMsgEvent event){
        if(event != null){
            LogUtils.i(TAG, "onMonitorEvent===>event: " + event.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if(event.isStatus()){
                        mBaseHeadLocImg.setImageResource(R.mipmap.icon_loc_success);
                        mBaseHeadLocTv.setVisibility(View.GONE);
                        mBaseBottomStopTitleTv.setText("");
                    }else{
                        mBaseHeadLocImg.setImageResource(R.mipmap.icon_no_loc);
                        mBaseHeadLocTv.setVisibility(View.VISIBLE);
                        mBaseBottomStopTitleTv.setText("未定位");
                    }
//                    mBatteryView.setPower((int)event.getBattery());
//                    mBatteryValueTv.setText((int) event.getBattery() + "%");
                    setRobotBattery((int)event.getBattery());
                }
            });
        }
    }

    private void setRobotBattery(int battery) {
        mBatteryValueTv.setText(battery + "%");
        if(battery <= 20){
            mBaseBatteryImg.setImageResource(R.mipmap.icon_battery_20);
        }else if(battery > 20 && battery < 40){
            mBaseBatteryImg.setImageResource(R.mipmap.icon_battery_25);
        }else if(battery >= 40 && battery < 60){
            mBaseBatteryImg.setImageResource(R.mipmap.icon_battery_50);
        }else if(battery >= 60 && battery < 100){
            mBaseBatteryImg.setImageResource(R.mipmap.icon_battery_75);
        }else if(battery == 100){
            mBaseBatteryImg.setImageResource(R.mipmap.icon_battery_100);
        }
    }

    @Subscribe
    public void onAlarmInfoEvent(AlarmInfoEvent event) {
        if(event != null) {
            LogUtils.i(TAG, "onAlarmInfoEvent===>current activity: " + getActivity());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    //无异常隐藏按钮
                    mBaseDraBtn.setVisibility(event.getCurrentAlarm() != null && event.getCurrentAlarm().size() > 0 ? View.VISIBLE : View.GONE);
                    if(!(getActivity() instanceof ExceptionActivity)) {
                        //进入异常界面
                        Intent intent = new Intent(getActivity(), ExceptionActivity.class);
                        startActivity(intent);
                    }
                }
            });
        }
    }

    private void showRobotNameDialog(){
        String robotName = GlobalRobotDataManager.getInstance().getRobotSn();
        if(robotName != null){
            if(mRobotDialog == null){
                mRobotDialog = new RobotDialog(this);
            }
            if(!mRobotDialog.isShowing()){
                mRobotDialog.show();
                mRobotDialog.setRobotName(robotName);
            }
        }
    }

    public void enterActivity(Class<?> cls){
        Intent intent = new Intent(getActivity(), cls);
        startActivity(intent);
    }

    /**
     * 是否为绑箱任务
     * @param type
     * @return
     */
    public boolean isBindTask(String type){
        return TextUtils.equals(type, ReportTask.TaskTypeEnum.TYPE_BIND.name());
    }

    /**
     * 是否为复合任务
     * @param type
     * @return
     */
    public boolean isRecheckTask(String type){
        return TextUtils.equals(type, ReportTask.TaskTypeEnum.TYPE_RECHECK.name());
    }

    /**
     * 是否为拣选任务
     * @param type
     * @return
     */
    public boolean isPickTask(String type){
        return TextUtils.equals(type, ReportTask.TaskTypeEnum.TYPE_PICK.name());
    }

    /**
     * 是否为泊车任务
     * @param type
     * @return
     */
    public boolean isParkTask(String type){
        return TextUtils.equals(type, ReportTask.TaskTypeEnum.TYPE_PARK.name());
    }

    /**
     * 是否为边拣边分
     * @param business
     * @return
     */
    public boolean isObFlowBusiness(String business){
        return TextUtils.equals(business, BusinessType.OB_FLOW.getType());
    }

    /**
     * 是否为集合单
     * @param business
     * @return
     */
    public boolean isObBusiness(String business){
        return TextUtils.equals(business, BusinessType.OB.getType());
    }

    /**
     * 是否有token缓存
     * @return
     */
    public boolean hasToken(){
        return GlobalRobotDataManager.getInstance().getTokenData() != null;
    }

    private void initBaseView(){
        List<AlarmDetailInfo> list = GlobalRobotDataManager.getInstance().getAlarmInfoList();
        LogUtils.i(TAG, "initBaseView===>alarm detail info: " + new Gson().toJson(list));
        if(list == null || list.size() == 0){
            mBaseDraBtn.setVisibility(View.GONE);
        }
        String robotName = GlobalRobotDataManager.getInstance().getRobotSn();
        if(robotName != null){
            LogUtils.i(TAG, "initBaseView===>robotName: " + robotName);
            mRobotNameTv.setText(StringUtils.extractIntegers(robotName));
        }
        MqttMsgEvent mqttMsgEvent = GlobalRobotDataManager.getInstance().getMqttMsgEvent();
        if(mqttMsgEvent != null){
            LogUtils.i(TAG, "initBaseView===>mqttMsgEvent: " + mqttMsgEvent.toString());
            mMqttStateImg.setImageResource(mqttMsgEvent.isConnected() ? R.mipmap.icon_mqtt_connect : R.mipmap.icon_mqtt_disconnect);
            mMqttStateTv.setVisibility(mqttMsgEvent.isConnected() ? View.GONE : View.VISIBLE);
        }
        MonitorMsgEvent monitorMsgEvent = GlobalRobotDataManager.getInstance().getMonitorMsgEvent();
        if(monitorMsgEvent != null){
            LogUtils.i(TAG, "initBaseView===>monitorMsgEvent: " + monitorMsgEvent.toString());
             mBaseHeadLocImg.setImageResource(monitorMsgEvent.isStatus() ? R.mipmap.icon_loc_success : R.mipmap.icon_no_loc);
             mBaseHeadLocTv.setVisibility(monitorMsgEvent.isStatus() ? View.GONE : View.VISIBLE);
             mBaseBottomStopTitleTv.setText(monitorMsgEvent.isStatus() ? "" : "未定位");
//            mBatteryView.setPower((int)monitorMsgEvent.getBattery());
//            mBatteryValueTv.setText((int) monitorMsgEvent.getBattery() + "%");
            setRobotBattery(monitorMsgEvent.getBattery());
        }
    }

}