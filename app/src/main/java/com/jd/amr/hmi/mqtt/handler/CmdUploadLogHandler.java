package com.jd.amr.hmi.mqtt.handler;

import android.content.Context;
import android.os.Environment;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.impl.model.TaskDataModel;
import com.jd.amr.hmi.model.data.http.OssUploadPreSignedUrlRequestBody;
import com.jd.amr.hmi.model.data.entity.OssUploadPreSignedUrlResponse;
import com.jd.amr.hmi.model.data.event.UploadLogEvent;
import com.jd.amr.hmi.model.data.mqtt.MqttResponseData;
import com.jd.amr.hmi.model.enums.RemoteCmdType;
import com.jd.amr.hmi.util.FileUtil;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.upgrade.OkHttpManager;
import com.jd.ugv.pad.common.utils.LogUtils;


import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import okhttp3.Response;

/**
 * 上传日志指令处理器
 */
public class CmdUploadLogHandler implements ICmdHandler {

    private static final String TAG = "CmdUploadLogHandler";

    private TaskDataModel taskDataModel;
    @Override
    public void handleMsg(Context context, String msg) {
        Type type = new TypeToken<MqttResponseData<UploadLogEvent>>() {
        }.getType();
        Gson gson = new Gson();
        MqttResponseData<UploadLogEvent> data = gson.fromJson(msg, type);
        if (data == null || data.getData() == null) {
            LogUtils.i(TAG, RemoteCmdType.CMD_UPLOAD_LOG.getName() + " data is null");
        } else {
            handleUploadLogData(context, data.getData());
        }
    }

    private void handleUploadLogData(Context context, UploadLogEvent data) {
        LogUtils.i(TAG, "handleUploadLogData===>data: " + data.toString());
        //获取日志文件
        File logFile = collectLogs(data.getFileKey(), data.getDate());
        if(logFile == null){
            LogUtils.i(TAG, "handleUploadLogData===>logFile is null");
            return;
        }
        //获取OSS上传预签名地址
        getOssUploadPreSignedUrlAndUpload(data.getFileKey(), data.getBucketName(), logFile);
    }
    
    /**
     * 收集日志
     * @return 日志文件路径
     */
    private File collectLogs(String fileName, String dataTime) {
        //从sdcard/sysLog目录下收集日志
        List<File> fileList = FileUtil.getFilesInDirectory(Constants.ROBOT_LOG_PATH, dataTime);
        if(fileList.isEmpty()){
            LogUtils.i(TAG, "collectLogs===>no log file");
            return null;
        }
        LogUtils.i(TAG, "collectLogs===>fileList: " + fileList.size());
        //压缩文件
        return FileUtil.compressFiles(fileList, fileName, Environment.getExternalStorageDirectory().getAbsolutePath());
    }
    
    /**
     * 获取OSS上传预签名地址并上传日志
     */
    private void getOssUploadPreSignedUrlAndUpload(String fileKey, String bucketName, File logFile) {
        if(taskDataModel == null){
            taskDataModel = new TaskDataModel();
        }
        OssUploadPreSignedUrlRequestBody request = new OssUploadPreSignedUrlRequestBody();
        request.setFileKey(fileKey);
        request.setBucketName(bucketName); // 设置适当的桶名
        taskDataModel.getOssUploadPreSignedUrl(request, new BaseCallBack<OssUploadPreSignedUrlResponse>() {
            @Override
            public void success(BaseResult<OssUploadPreSignedUrlResponse> result) {
                if (result != null && result.getData() != null) {
                    uploadLogs(logFile, result.getData().getUploadUrl());
                } else {
                    LogUtils.e(TAG, "Failed to get OSS upload pre-signed URL");
                }
            }

            @Override
            public boolean failOrError(BaseResult<OssUploadPreSignedUrlResponse> result, String message) {
                LogUtils.e(TAG, "Failed to get OSS upload pre-signed URL: " + message);
                return false;
            }

            @Override
            public void error(String e) {
                LogUtils.e(TAG, "Failed to get OSS upload pre-signed URL: " + e);
            }
        });
    }
    
    /**
     * 上传日志
     * @param logFile 日志文件
     * @param uploadUrl 上传地址
     */
    private void uploadLogs(File logFile, String uploadUrl) {
        if(logFile != null && uploadUrl != null) {
            LogUtils.i(TAG, "uploadLogs===>logFile: " + logFile.getAbsolutePath());
            OkHttpManager.getInstance().uploadFile(logFile, uploadUrl, new OkHttpManager.HttpCallBack() {
                @Override
                public void onResponse(JsonObject jsonObject) {
                    LogUtils.i(TAG, "uploadLogs===>success, delete log file");
                    FileUtil.deleteFolderFile(logFile.getAbsolutePath(), true);
                }

                @Override
                public void onResponse(Response response) {

                }

                @Override
                public void onFailure(String errorMsg) {
                    LogUtils.i(TAG, "uploadLogs===>failed: " + errorMsg);
                }
            });
        }
    }

    @Override
    public String cmdType() {
        return RemoteCmdType.CMD_UPLOAD_LOG.getType();
    }
}
