/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 ***************************************************************************/

package com.jd.amr.hmi.util;

import static com.jd.amr.hmi.common.Constants.FOLDER_UPDATE;
import static com.jd.ugv.pad.common.utils.LogUtils.TAG_TYPE_OTA;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.gson.JsonObject;
import com.jd.amr.hmi.Amr;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.model.data.entity.OtaTaskStatusInfo;
import com.jd.amr.hmi.model.enums.OtaTaskStatus;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.upgrade.OkHttpManager;
import com.jd.ugv.pad.common.utils.SharedPreferencesUtil;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

import okhttp3.Response;


/**
 * Create by chenruihua8 on 2021/2/25
 * Description:utils for version management
 */
public class VersionUtil {
    private final String TAG = getClass().getSimpleName();
    private static final String PKG_UGV_TOOL = "com.jd.ugvTool";
    private static final String SERVICE_UGV_TOOL = "com.jd.ugvTool.ToolService";
    private static final String ACTION_UGV_TOOL_INSTALL = "com.jd.ugvtool.install";
    private static final String APK_NAME_UGV_TOOL = "/ugvtool.apk";
    private static final String KEY_APK_PATH = "apk_path";
    private static final String KEY_UGV_START_PATH = "start_path";

    public static final String ACTION_INSTALL_COMPLETE = "com.example.ACTION_INSTALL_COMPLETE";

    private static final String VALUE_UGV_START_PATH = "com.jd.ugv.pad.liveness/com.jd.ugv.pad" +
            ".view.activity.HomeActivity";

    private static final int REQUEST_CODE_INSTALL = 1;
    private String versionName;
    private String pkgName;
    private int versionCode;
    private Context mContext;
    private Toast processToast = null;

    private static final long RETRY_DELAY_MILLIS = 60 * 1000; //30s
    private static final int MAX_RETRY_TIME = 3;
    private int retryNum = 0;

    private Intent mJdMqttService;

    private VersionUtil() {
        this.mContext = Amr.getInstance().getApplicationContext();
    }

    public static VersionUtil getInstance() {
        return LazyHolder.INSTANCE;
    }

    private static class LazyHolder {
        private static final VersionUtil INSTANCE = new VersionUtil();
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public int getVersionCode() {
        LogUtils.i(TAG_TYPE_OTA, "versionCode = " + versionCode);
        return versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }


    public void initCurPkgData() {
        if (null == versionName || null == pkgName) {
            try {
                PackageManager packageManager = mContext.getPackageManager();
                PackageInfo info = packageManager.getPackageInfo(mContext.getPackageName(), 0);
                versionName = info.versionName;
                pkgName = info.packageName;
                versionCode = info.versionCode;
                Log.i(TAG, "parsePkg: versionName = " + versionName + " pkgName = " + pkgName + " versionCode = " + versionCode);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * <p>
     * mkdir update folder
     * </p>
     */
    public File mkdirUpdateFolder() {
//        Log.i(TAG, "mkdirUpdateFolder: " + FOLDER_UPDATE);
//        File folder = new File(FOLDER_UPDATE);
//        if (!folder.exists() || !folder.isDirectory()) {
//            if (!folder.mkdirs()) {
//                Log.e(TAG, "existPkgPath: create update folder failed");
//            }
//        }
        // 优先使用应用专属存储
        File targetDir = new File(Amr.getInstance().getExternalFilesDir(null), FOLDER_UPDATE);
        Log.i(TAG, "mkdirUpdateFolder: " + targetDir.getAbsolutePath());
        if (!targetDir.exists()) {
            if (!targetDir.mkdirs()) {
                Log.e("Storage", "Failed to create: " + targetDir.getAbsolutePath());
                return null;
            }
        }
        return targetDir;
    }

    /**
     * <p>
     *
     * </p>
     *
     * @param path
     * @return true ; false
     * @throws
     */
    private boolean checkDownloadedPkg(String path) {
        PackageInfo info = getPackageInfoFromAPKFile(path);
        if (null == info) {
            Log.e(TAG, "checkDownloadPkg: package info is null");
            return false;
        }
        return true;
    }

    /**
     * <p>
     * get packageInfo from apk file
     * </p>
     *
     * @param
     * @return packageInfo or null
     */
    private PackageInfo getPackageInfoFromAPKFile(String apkFilepath) {
        PackageInfo info = null;
        PackageManager packageManager = mContext.getPackageManager();
        info = packageManager.getPackageArchiveInfo(apkFilepath, PackageManager.GET_ACTIVITIES);
        if (null == info) {
            info = packageManager.getPackageArchiveInfo(apkFilepath, PackageManager.GET_SERVICES);
            if (null == info) {
                info = packageManager.getPackageArchiveInfo(apkFilepath, 0);
            }
        }

        return info;
    }

    /**
     * <p>
     * download apk file for uograde version
     * </p>
     *
     * @param url
     * @param fileName
     * @param downloadLength
     * @param contentLength
     */
    public void downLoadApkFile(@NonNull String url, @NonNull String fileName, final int downloadLength,
                                final int contentLength, String taskNum, String versionName, OtaTaskStatusInfo taskStatusInfo) {
        LogUtils.i(TAG_TYPE_OTA, "downLoadApkFile: url = " + url + " fileName = " + fileName + " downloadLength" + " = " + downloadLength + " contentLength = " + contentLength + ", taskNum: " + taskNum);
        GlobalRobotDataManager.getInstance().updateOtaTaskStatus(taskStatusInfo, OtaTaskStatus.DOWNLOADING.getCode(), OtaTaskStatus.DOWNLOADING.getDescription());
        OkHttpManager.getInstance().downLoadFile(url, downloadLength, contentLength,
                new OkHttpManager.HttpCallBack() {
                    @Override
                    public void onResponse(JsonObject jsonObject) {
                        //do nothing!
                        LogUtils.i(TAG_TYPE_OTA, "onResponse0");
                    }

                    @Override
                    public void onResponse(Response response) {
                        if (null == response || null == response.body()) {
                            LogUtils.d(TAG_TYPE_OTA, "downLoadFile onResponse: response or body is null");
                            return;
                        }

                        InputStream inputStream = null;
                        byte[] buffer = new byte[2048];
                        int len = 0;
                        FileOutputStream fileOutputStream = null;
                        double current = downloadLength;

                        double total = contentLength;
                        if (total == 0) {
                            total = response.body().contentLength();
                        }
                        try {
                            String destPath = Amr.getInstance().getExternalFilesDir(null) + FOLDER_UPDATE + fileName;
                            LogUtils.i(TAG_TYPE_OTA, "downLoadFile onResponse: destPath = " + destPath);
                            File file = new File(destPath);
                            if (!file.exists()) {
                                file.createNewFile();
                            }

                            if (contentLength != 0 && downloadLength != 0) {
                                fileOutputStream = new FileOutputStream(file, true);
                            } else {
                                fileOutputStream = new FileOutputStream(file);
                            }

                            int progress = 0;
                            inputStream = response.body().byteStream();
                            while ((len = inputStream.read(buffer)) != -1) {
                                current += len;
                                fileOutputStream.write(buffer, 0, len);
                                int curDownloadProgress = (int) (current * 1.0f / total * 100);
                                if (progress != curDownloadProgress) {
                                    progress = curDownloadProgress;
                                    LogUtils.i(TAG_TYPE_OTA, "downLoadFile downloading: " + progress);
                                }
                            }
                            fileOutputStream.flush();
                            if (total > 0 && current == total) {
                                SharedPreferencesUtil.getInstance().remove(Constants.TOTAL_SIZE);
                                SharedPreferencesUtil.getInstance().remove(Constants.DOWNLOADED_SIZE);
                                GlobalRobotDataManager.getInstance().setOtaTaskStatusInfo(taskStatusInfo);
                                GlobalRobotDataManager.getInstance().updateOtaTaskStatus(taskStatusInfo, OtaTaskStatus.DOWNLOAD_COMPLETED.getCode(), OtaTaskStatus.DOWNLOAD_COMPLETED.getDescription());
                                if (checkDownloadedPkg(destPath)) {
                                    GlobalRobotDataManager.getInstance().updateOtaTaskStatus(taskStatusInfo, OtaTaskStatus.INSTALLING.getCode(), OtaTaskStatus.INSTALLING.getDescription());
                                    upgradeUgv(destPath);
                                } else {
                                    Log.e(TAG_TYPE_OTA, "downLoadFile: parse pkg error.");
                                    GlobalRobotDataManager.getInstance().updateOtaTaskStatus(taskStatusInfo, OtaTaskStatus.DOWNLOAD_FAILED.getCode(), OtaTaskStatus.DOWNLOAD_FAILED.getDescription());
                                }
                            }
                        } catch (IOException e) {
                            LogUtils.e(TAG_TYPE_OTA, "downLoadFile exception: " + e);
                            GlobalRobotDataManager.getInstance().updateOtaTaskStatus(taskStatusInfo, OtaTaskStatus.DOWNLOAD_FAILED.getCode(), "下载异常：" + e.getMessage());
                            SharedPreferencesUtil.getInstance().setInt(Constants.DOWNLOADED_SIZE, (int) current);
                            SharedPreferencesUtil.getInstance().setInt(Constants.TOTAL_SIZE, (int) total);
                            retryDownload(url, fileName, (int) current, (int) total, taskNum, versionName, taskStatusInfo);
                        } finally {
                            try {
                                if (inputStream != null) {
                                    inputStream.close();
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            }

                            try {
                                if (fileOutputStream != null) {
                                    fileOutputStream.close();
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onFailure(String errorMsg) {
                        //do nothing!
                        LogUtils.i(TAG_TYPE_OTA, "onFailure: " + errorMsg);
                        int downLength = SharedPreferencesUtil.getInstance().getInt(Constants.DOWNLOADED_SIZE);
                        int total = SharedPreferencesUtil.getInstance().getInt(Constants.TOTAL_SIZE);
                        retryDownload(url, fileName, downLength, total, taskNum, versionName, taskStatusInfo);
                    }
                });
    }

    private void retryDownload(String url, String fileName, final int downloadLength,
                               final int contentLength, String taskNum, String versionName, OtaTaskStatusInfo taskStatusInfo) {
        try {
            Thread.sleep(RETRY_DELAY_MILLIS);
        } catch (InterruptedException interruptedException) {
            interruptedException.printStackTrace();
        }
        LogUtils.e(TAG_TYPE_OTA, "retryDownload retryNum: " + retryNum);
        if (retryNum < MAX_RETRY_TIME) {
            downLoadApkFile(url, fileName, downloadLength, contentLength, taskNum, versionName, taskStatusInfo);
            retryNum++;
        }
    }

    /**
     * <p>
     * upgrade ugv
     * </p>
     *
     * @param apkFilePath
     */
    public void upgradeUgv(@NonNull String apkFilePath) {
        LogUtils.i(TAG_TYPE_OTA, "installPkg: apkFilePath = " + apkFilePath);
        File file = new File(apkFilePath);
        if (!file.exists()) {
            LogUtils.i(TAG_TYPE_OTA, "installPkg: file is not exist!");
            return;
        }
        boolean result = installApk(apkFilePath);
        if (result) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    LogUtils.i(TAG_TYPE_OTA, "启动app");
                    exec("reboot");
                }
            }, 100);
        }
    }


    private String exec(String command) {
        Process process = null;
        BufferedReader reader = null;
        InputStreamReader is = null;
        DataOutputStream os = null;
        try {
            process = Runtime.getRuntime().exec("su");
            is = new InputStreamReader(process.getInputStream());
            reader = new BufferedReader(is);
            os = new DataOutputStream(process.getOutputStream());
            os.writeBytes(command + "\n");
            os.writeBytes("exit\n");
            os.flush();
            int read;
            char[] buffer = new char[4096];
            StringBuilder output = new StringBuilder();
            while ((read = reader.read(buffer)) > 0) {
                output.append(buffer, 0, read);
            }
            process.waitFor();
            return output.toString();
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (is != null) {
                    is.close();
                }
                if (reader != null) {
                    reader.close();
                }
                if (process != null) {
                    process.destroy();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }



    /**
     * <p>
     * install apk file
     * </p>
     *
     * @param apkPath
     * @return true: install success false: install failed
     */
    private boolean installApk(String apkPath) {
        Log.i(TAG_TYPE_OTA, "installApk:start install...");

        if (TextUtils.isEmpty(apkPath)) {
            throw new IllegalArgumentException("Please check apk file path!");
        }

        File file = new File(apkPath);
        if (!file.exists()) {
            throw new IllegalArgumentException("Please check apk file exists!");
        }

        boolean result = false;
        Process process = null;
        OutputStream outputStream = null;
        BufferedReader errorStream = null;
        try {
            process = Runtime.getRuntime().exec("su");
            outputStream = process.getOutputStream();

            String command = "pm install -r " + apkPath + "\n";
            outputStream.write(command.getBytes());
            outputStream.flush();
            outputStream.write("exit\n".getBytes());
            outputStream.flush();
            process.waitFor();
            errorStream = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            StringBuilder msg = new StringBuilder();
            String line;
            while ((line = errorStream.readLine()) != null) {
                msg.append(line);
            }
            Log.d(TAG_TYPE_OTA, "install msg is " + msg);
            if (!msg.toString().contains("Failure")) {
                result = true;
            }
        } catch (Exception e) {
            Log.e(TAG_TYPE_OTA, e.getMessage(), e);
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (errorStream != null) {
                    errorStream.close();
                }
            } catch (IOException e) {
                outputStream = null;
                errorStream = null;
                process.destroy();
            }
        }
        Log.i(TAG_TYPE_OTA, "installApk: start install finish" + (result ? "success" : "failed"));
        return result;
    }
}
