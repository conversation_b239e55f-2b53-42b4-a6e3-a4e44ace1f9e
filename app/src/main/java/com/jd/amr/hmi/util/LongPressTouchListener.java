package com.jd.amr.hmi.util;

import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;

import com.jd.ugv.pad.common.utils.LogUtils;

public class LongPressTouchListener implements View.OnTouchListener{

    private static final long LONG_PRESS_TIME = 10000; // 10秒
    private static final float MOVE_TOLERANCE = 20f;   // 移动容差（像素）

    private final Handler handler = new Handler();
    private Runnable longPressRunnable;
    private float lastX, lastY;
    private boolean isTracking;
    private LongPressListener listener;

    public interface LongPressListener {
        void onLongPressSuccess();
        void onLongPressProgress(int progress); // 进度回调（0-100）
        void onLongPressCancelled();
    }

    public LongPressTouchListener(LongPressListener listener) {
        this.listener = listener;
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startTracking(event.getX(), event.getY());
                return true;

            case MotionEvent.ACTION_MOVE:
                if (isTracking && hasMoved(event)) {
                    cancelTracking();
                }
                return true;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                cancelTracking();
                return true;
        }
        return false;
    }

    private void startTracking(float x, float y) {
        lastX = x;
        lastY = y;
        isTracking = true;

        // 主计时任务
        longPressRunnable = new Runnable() {
            private long startTime = System.currentTimeMillis();

            @Override
            public void run() {
                if (!isTracking) return;

                long elapsed = System.currentTimeMillis() - startTime;
                int progress = (int) ((elapsed * 100) / LONG_PRESS_TIME);

                // 更新进度
                if (listener != null) {
                    listener.onLongPressProgress(Math.min(progress, 100));
                }
                if (elapsed >= LONG_PRESS_TIME) {
                    completeTracking();
                } else {
                    // 每100ms更新一次进度
                    handler.postDelayed(this, 100);
                }
            }
        };

        handler.postDelayed(longPressRunnable, 100);
    }

    private boolean hasMoved(MotionEvent event) {
        float dx = Math.abs(event.getX() - lastX);
        float dy = Math.abs(event.getY() - lastY);
        return dx > MOVE_TOLERANCE || dy > MOVE_TOLERANCE;
    }

    private void completeTracking() {
        isTracking = false;
        handler.removeCallbacks(longPressRunnable);
        if (listener != null) {
            listener.onLongPressSuccess();
        }
    }

    private void cancelTracking() {
        isTracking = false;
        handler.removeCallbacks(longPressRunnable);
        if (listener != null) {
            listener.onLongPressCancelled();
        }
    }

    public void release() {
        cancelTracking();
        listener = null;
    }
}
