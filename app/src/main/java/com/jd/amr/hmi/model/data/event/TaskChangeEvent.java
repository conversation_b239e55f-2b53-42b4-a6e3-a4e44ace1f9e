package com.jd.amr.hmi.model.data.event;

/**
 * 任务变更事件
 */
public class TaskChangeEvent {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务描述
     */
    private String taskDesc;

    public TaskChangeEvent() {
    }

    public TaskChangeEvent(String taskId, String taskStatus, String taskType, String taskDesc) {
        this.taskId = taskId;
        this.taskStatus = taskStatus;
        this.taskType = taskType;
        this.taskDesc = taskDesc;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    @Override
    public String toString() {
        return "TaskChangeEvent{" +
                "taskId='" + taskId + '\'' +
                ", taskStatus='" + taskStatus + '\'' +
                ", taskType='" + taskType + '\'' +
                ", taskDesc='" + taskDesc + '\'' +
                '}';
    }
}
