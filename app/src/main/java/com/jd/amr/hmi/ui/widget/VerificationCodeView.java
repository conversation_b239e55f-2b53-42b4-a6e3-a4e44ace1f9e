package com.jd.amr.hmi.ui.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatEditText;

import com.jd.amr.hmi.R;

public class VerificationCodeView extends AppCompatEditText {

    private static final String TAG = VerificationCodeView.class.getSimpleName();

    /**
     * 间隔
     */
    private float space = 32;
    /**
     * 验证码长度
     */
    private int codeLength = 6;
    /**
     * 未输入下划线颜色
     */
    private int nextLineColor = Color.RED;
    /**
     * 当前输入下划线颜色
     */
    private int otherLineColor = Color.WHITE;
    /**
     * 文字颜色
     */
    private int textColor = Color.BLACK;

    /**
     * 文本字体大小
     */
    private float textSize = 64f;

    /**
     * 验证码框下划线高
     */
    private int lineHeight = 8;
    /**
     * 文本画笔
     */
    private Paint mTextPaint;
    /**
     * 线画笔
     */
    private Paint mLinePaint;


    /**
     * 输入结束监听
     */
    private OnInputFinishListener mOnInputFinishListener;

    private StringBuilder mStringBuilder;


    /**
     * 构造方法
     *
     * @param context
     * @param attrs
     */
    public VerificationCodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.VerificationCodeViewStyleable);
        codeLength = array.getInteger(R.styleable.VerificationCodeViewStyleable_verifyCodeLength, codeLength);
        nextLineColor = array.getInteger(R.styleable.VerificationCodeViewStyleable_verifyNextLineColor, nextLineColor);
        otherLineColor = array.getInteger(R.styleable.VerificationCodeViewStyleable_verifyOtherLineColor, otherLineColor);
        space = array.getDimension(R.styleable.VerificationCodeViewStyleable_verifySpace, space);
        lineHeight = (int) array.getDimension(R.styleable.VerificationCodeViewStyleable_verifyLineHeight, lineHeight);
        textSize = array.getDimension(R.styleable.VerificationCodeViewStyleable_verifyTextSize, textSize);
        textColor = array.getInteger(R.styleable.VerificationCodeViewStyleable_verifyTextColor, textColor);

        // 初始化文字画笔
        mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.setColor(textColor);
        mTextPaint.setTextSize(textSize);

        // 初始化下划线
        mLinePaint = new Paint();
        mLinePaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mLinePaint.setAntiAlias(true);
        mLinePaint.setStrokeWidth(lineHeight);
        mLinePaint.setStrokeCap(Paint.Cap.ROUND);

        mStringBuilder = new StringBuilder(codeLength);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //获取宽高的尺寸值 固定值的宽度
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);
        //设置测量的宽高
        setMeasuredDimension(widthSize, heightSize);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // 计算每个密码框宽度
        int rectWidth = (int) ((getWidth() - space * (codeLength - 1) - lineHeight * codeLength) / codeLength);
        int bottom = getHeight();

        // 绘制内容
        char[] strArray = mStringBuilder.toString().toCharArray();
        for (int index = 0; index < codeLength; index++) {
            int left;
            int right;
            if (index == 0) {
                left = lineHeight;
                right = rectWidth;
            } else {
                left = (int) ((lineHeight + space + rectWidth) * index);
                right = left + rectWidth;
            }

            //角标小于已输入内容  文字
            if (index < mStringBuilder.length()) {
                //绘制文本
                int cx = (int) (left + rectWidth / 2 - space);
                int cy = px2dp(getContext(), textSize);
                char str = strArray[index];
                canvas.drawText(String.valueOf(str), cx, cy, mTextPaint);
            }
            //角标等于已输入内容  红线
            else if (index == mStringBuilder.length()) {
                mLinePaint.setColor(nextLineColor);
                canvas.drawLine(left, bottom / 2, right, bottom / 2, mLinePaint);
            }
            //角标大于已输入内容  白线
            else {
                mLinePaint.setColor(otherLineColor);
                canvas.drawLine(left, bottom / 2, right, bottom / 2, mLinePaint);

            }
        }
    }

    @Override
    protected void onTextChanged(CharSequence text, int start, int lengthBefore, int lengthAfter) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter);
    }

    public String getInputString(){
        return mStringBuilder.toString();
    }

    public void addTextChanged(String str) {
        if (mStringBuilder.length() < codeLength) {
            mStringBuilder.append(str);
            invalidate();
            if (mStringBuilder.length() == codeLength && mOnInputFinishListener != null) {
                mOnInputFinishListener.onInputFinish(mStringBuilder.toString());
            }
        }
    }

    public void deleteTextChanged() {
        if (mStringBuilder.length() > 0) {
            mStringBuilder.deleteCharAt(mStringBuilder.length() - 1);
            invalidate();
        }
    }

    public void resetTextChanged() {
        mStringBuilder.delete(0, mStringBuilder.length());
        invalidate();
    }


    public interface OnInputFinishListener {
        /**
         * 密码输入结束监听
         *
         * @param password
         */
        void onInputFinish(String password);
    }

    /**
     * 设置输入完成监听
     *
     * @param onInputFinishListener
     */
    public void setOnInputFinishListener(OnInputFinishListener onInputFinishListener) {
        this.mOnInputFinishListener = onInputFinishListener;
    }

    private int px2dp(Context context, float pxValue) {
        // 获取当前手机的像素密度（1个dp对应几个px）
        float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f); // 四舍五入取整
    }
}
