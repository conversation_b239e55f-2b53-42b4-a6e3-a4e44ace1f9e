package com.jd.amr.hmi.model.data.entity;

import java.io.Serializable;

/**
 * 简单设备版本信息实体类
 */
public class OtaTaskVersion implements Serializable {
    /**
     * 产品密钥
     */
    private String productKey;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 版本号
     */
    private String version;

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "SimpleDeviceVersion{" +
                "productKey='" + productKey + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", timestamp=" + timestamp +
                ", appName='" + appName + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
