/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jd.amr.hmi.model.data.mqtt;

/**
 * <p>
 * mqtt请求头
 * </p >
 *
 * <AUTHOR>
 * @date： 2022/7/21
 */
public class MqttRequestHeader {

    private String messageType;

    //请求ID：13位毫秒时间戳+6位顺序号
    private Long requestId;

    //发送用户名
    private String sendName;

    //接收用户名
    private String receiveName;

    //请求时间
    private Long requestTime;

    //过期毫秒数:单位毫秒,不传默认不过期
    private Integer expireMilliSecond;

    private Boolean needResponse;


    public MqttRequestHeader(){

    }

    public MqttRequestHeader(String messageType, Long requestId, String robotSn, Long requestTime, Integer expireMilliSecond, Boolean needResponse) {
        this.messageType = messageType;
        this.requestId = requestId;
        this.sendName = robotSn;
        this.requestTime = requestTime;
        this.expireMilliSecond = expireMilliSecond;
        this.needResponse = needResponse;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public Long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Long requestTime) {
        this.requestTime = requestTime;
    }

    public Integer getExpireMilliSecond() {
        return expireMilliSecond;
    }

    public void setExpireMilliSecond(Integer expireMilliSecond) {
        this.expireMilliSecond = expireMilliSecond;
    }

    public Boolean getNeedResponse() {
        return needResponse;
    }

    public void setNeedResponse(Boolean needResponse) {
        this.needResponse = needResponse;
    }

    @Override
    public String toString() {
        return "MqttRequestHeader{" +
                "messageType='" + messageType + '\'' +
                ", requestId=" + requestId +
                ", sendName='" + sendName + '\'' +
                ", receiveName='" + receiveName + '\'' +
                ", requestTime=" + requestTime +
                ", expireMilliSecond=" + expireMilliSecond +
                ", needResponse=" + needResponse +
                '}';
    }
}
