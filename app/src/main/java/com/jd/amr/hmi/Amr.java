package com.jd.amr.hmi;

import android.app.Application;

import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.util.VersionUtil;
import com.jd.ugv.pad.common.upgrade.OkHttpManager;
import com.jd.ugv.pad.common.utils.SharedPreferencesUtil;

public class Amr extends Application {

    private static Amr instance;
    public static Amr getInstance() {
        return instance;
    }
    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        OkHttpManager.init(instance);
        SharedPreferencesUtil.getInstance().init(this, Constants.SP_KEY);
        VersionUtil.getInstance().initCurPkgData();
        VersionUtil.getInstance().mkdirUpdateFolder();
    }

}
