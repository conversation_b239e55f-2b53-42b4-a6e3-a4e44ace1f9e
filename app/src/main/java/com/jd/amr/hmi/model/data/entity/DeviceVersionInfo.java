package com.jd.amr.hmi.model.data.entity;

import java.util.List;

/**
 * 设备版本信息实体类
 */
public class DeviceVersionInfo {

    private String productKey;
    private String deviceName;
    private Long timestamp;
    private List<VersionInfoDetail> versionInfoList;

    public String getProductKey() { return productKey; }
    public void setProductKey(String productKey) { this.productKey = productKey; }

    public String getDeviceName() { return deviceName; }
    public void setDeviceName(String deviceName) { this.deviceName = deviceName; }

    public Long getTimestamp() { return timestamp; }
    public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }

    public List<VersionInfoDetail> getVersionInfoList() { return versionInfoList; }
    public void setVersionInfoList(List<VersionInfoDetail> versionInfoList) { this.versionInfoList = versionInfoList; }

    @Override
    public String toString() {
        return "DeviceVersionInfo{" +
                "productKey='" + productKey + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", timestamp=" + timestamp +
                ", versionInfoList=" + versionInfoList +
                '}';
    }

}
