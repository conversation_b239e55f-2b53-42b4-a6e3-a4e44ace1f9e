package com.jd.amr.hmi.model.data.entity;

import com.jd.ugv.pad.common.base.BaseResponse;

public class OssUploadPreSignedUrlResponse extends BaseResponse {
    private String fileKey;
    private String bucketName;
    private String uploadUrl;

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getUploadUrl() {
        return uploadUrl;
    }

    public void setUploadUrl(String uploadUrl) {
        this.uploadUrl = uploadUrl;
    }

    @Override
    public String toString() {
        return "OssUploadPreSignedUrlResponse{" +
                "fileKey='" + fileKey + '\'' +
                ", bucketName='" + bucketName + '\'' +
                ", uploadUrl='" + uploadUrl + '\'' +
                '}';
    }
}
