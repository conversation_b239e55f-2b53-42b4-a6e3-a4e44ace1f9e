package com.jd.amr.hmi.manager.http.impl.request;

import com.google.gson.Gson;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.ITaskApi;
import com.jd.amr.hmi.manager.http.base.AndroidResponse;
import com.jd.amr.hmi.model.data.entity.ParkPointListResponse;
import com.jd.amr.hmi.model.data.entity.TaskInfo;
import com.jd.amr.hmi.model.data.entity.OssUploadPreSignedUrlResponse;
import com.jd.amr.hmi.model.data.http.OssUploadPreSignedUrlRequestBody;
import com.jd.amr.hmi.model.data.http.CommonRequest;
import com.jd.ugv.pad.common.base.BaseDataRequest;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.TokenData;

import io.reactivex.rxjava3.core.Observable;
import okhttp3.MediaType;
import okhttp3.RequestBody;


public class TaskApiRequest extends BaseDataRequest {

    private ITaskApi taskApi;

    public TaskApiRequest(){
        super();
        taskApi = create(ITaskApi.class);
    }

    @Override
    protected String getBaseUrl() {
        return BuildConfig.PROXY_URL;
    }

    @Override
    protected TokenData getTokenData() {
        return GlobalRobotDataManager.getInstance().getTokenData();
    }

    public Observable<BaseResult<AndroidResponse>> bindShelfRequest(CommonRequest commonRequest){
        return taskApi.bindShelfApi(generateRequestBody(commonRequest));
    }

    public Observable<BaseResult<AndroidResponse>> taskCompleteRequest(CommonRequest commonRequest){
        return taskApi.reportTaskComplete(generateRequestBody(commonRequest));
    }

    /**
     * 获取任务信息请求
     * @param commonRequest 请求体
     * @return 请求结果
     */
    public Observable<BaseResult<TaskInfo>> getTaskInfoRequest(CommonRequest commonRequest){
        return taskApi.getTaskInfo(generateRequestBody(commonRequest));
    }

    /**
     * 获取地图点位信息请求
     * @param commonRequest 请求体
     * @return 请求结果
     */
    public Observable<BaseResult<ParkPointListResponse>> getPointListRequest(CommonRequest commonRequest){
        return taskApi.getPointList(generateRequestBody(commonRequest));
    }

    /**
     * 获取OSS上传预签名地址请求
     * @param request OSS上传预签名地址请求体
     * @return 请求结果
     */
    public Observable<BaseResult<OssUploadPreSignedUrlResponse>> getOssUploadPreSignedUrlRequest(CommonRequest request) {
        return taskApi.getOssUploadPreSignedUrl(generateRequestBody(request));
    }

    public RequestBody generateRequestBody(CommonRequest commonRequest) {
        String requestString = commonRequest.build();
        RequestBody body = RequestBody.create(okhttp3.MediaType.parse("application/json;charset=UTF-8"), requestString);
        return body;
    }
}
