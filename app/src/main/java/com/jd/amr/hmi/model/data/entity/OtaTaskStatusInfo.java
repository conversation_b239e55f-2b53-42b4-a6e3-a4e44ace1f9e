package com.jd.amr.hmi.model.data.entity;

import com.jd.amr.hmi.model.enums.OtaTaskStatus;

import java.io.Serializable;

/**
 * OTA任务信息实体类
 */
public class OtaTaskStatusInfo implements Serializable {
    /**
     * 产品密钥
     */
    private String productKey;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 版本号
     */
    private String version;

    /**
     * 任务编号
     */
    private String issueTaskNumber;

    /**
     * 任务状态
     * @see OtaTaskStatus
     */
    private int status;

    /**
     * 备注信息
     */
    private String remark;

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getIssueTaskNumber() {
        return issueTaskNumber;
    }

    public void setIssueTaskNumber(String issueTaskNumber) {
        this.issueTaskNumber = issueTaskNumber;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取任务状态枚举值
     *
     * @return 任务状态枚举值
     */
    public OtaTaskStatus getStatusEnum() {
        return OtaTaskStatus.fromCode(status);
    }

    /**
     * 判断任务是否处于终态
     *
     * @return 是否为终态
     */
    public boolean isTerminalState() {
        OtaTaskStatus statusEnum = getStatusEnum();
        return statusEnum != null && statusEnum.isTerminalState();
    }

    /**
     * 判断任务是否失败
     *
     * @return 是否失败
     */
    public boolean isFailedState() {
        OtaTaskStatus statusEnum = getStatusEnum();
        return statusEnum != null && statusEnum.isFailedState();
    }

    /**
     * 判断任务是否进行中
     *
     * @return 是否进行中
     */
    public boolean isInProgress() {
        OtaTaskStatus statusEnum = getStatusEnum();
        return statusEnum != null && statusEnum.isInProgress();
    }

    @Override
    public String toString() {
        return "OtaTaskInfo{" +
                "productKey='" + productKey + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", timestamp=" + timestamp +
                ", appName='" + appName + '\'' +
                ", version='" + version + '\'' +
                ", issueTaskNumber='" + issueTaskNumber + '\'' +
                ", status=" + status +
                ", statusEnum=" + getStatusEnum() +
                ", remark='" + remark + '\'' +
                '}';
    }
}
