package com.jd.amr.hmi.service;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.google.gson.Gson;
import com.iscas.rcljava.entity.topic.MqttState;
import com.iscas.rcljava.entity.topic.RobotMonitorState;
import com.iscas.rcljava.entity.topic.TaskInfo;
import com.iscas.rcljava.entity.topic.VehicleInfo;
import com.iscas.rcljava.robot.AbsMessageListener;
import com.iscas.rcljava.socket.ConnectionStatusListener;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.socket.IRosService;
import com.jd.amr.hmi.manager.socket.impl.RosServiceImpl;
import com.jd.amr.hmi.model.data.entity.PointInfo;
import com.jd.amr.hmi.model.data.entity.TaskStartInfo;
import com.jd.amr.hmi.model.data.event.MonitorMsgEvent;
import com.jd.amr.hmi.model.data.event.MqttMsgEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoEndEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.model.data.event.VehicleInfoEvent;
import com.jd.ugv.pad.common.utils.LogUtils;


import org.greenrobot.eventbus.EventBus;

import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * 机器人数据监听Service
 */
public class RobotRemoteService extends Service {

    private static final String TAG = "RobotRemoteService";

    private IRosService mRosService;

    private boolean isRosConnected = false;

    private boolean isExist = false;

    public static final String ROBOT_REMOTE_INIT = "ROBOT_REMOTE_INIT";

    public static final int ROBOT_RECONNECT_MSG = 1;

    public static final int ROBOT_GET_VEHICLE_INFO = 2;

    private ScheduledExecutorService scheduledExecutorService;

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case ROBOT_RECONNECT_MSG -> {
                    LogUtils.i(TAG, "reconnect robot");
                    initRemote();
                }
                case ROBOT_GET_VEHICLE_INFO -> {
                    LogUtils.i(TAG, "get vehicle info");
                    mRosService.registerVehicleInfo(mVehicleInfoListener);
                }
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        mRosService = new RosServiceImpl();
        scheduledExecutorService = new ScheduledThreadPoolExecutor(1);
        initNotification();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            LogUtils.i(TAG, "onStartCommand===>action: " + action);
            switch (action) {
                case ROBOT_REMOTE_INIT:
                    initRemote();
                    break;
                default:
                    break;
            }
        }
        //设置服务死掉后不自动拉活
        return START_NOT_STICKY;
    }

    @SuppressLint("ForegroundServiceType")
    private void initNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel("REMOTE_CHANNEL_ID", "REMOTE_CHANNEL_NAME", NotificationManager.IMPORTANCE_LOW);
            NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (manager == null) {
                return;
            }
            manager.createNotificationChannel(channel);
            Notification notification = new NotificationCompat.Builder(this, "REMOTE_CHANNEL_ID")
                    .setAutoCancel(true)
                    .setCategory(Notification.CATEGORY_SERVICE)
                    .setOngoing(true)
                    .setPriority(NotificationManager.IMPORTANCE_LOW)
                    .build();

            startForeground(103, notification);
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void initRemote(){
        if(scheduledExecutorService == null){
            scheduledExecutorService = new ScheduledThreadPoolExecutor(1);
        }
        scheduledExecutorService.execute(() -> {
            connectRobot();
        });
    }

    private void connectRobot() {
        if (isRosConnected) {
            return;
        }
        LogUtils.i(TAG, "robot 连接中, 当前线程：" + Thread.currentThread().toString());
        mRosService.connect(this, new ConnectionStatusListener() {
            @Override
            public void onConnect() {
                isRosConnected = true;
                GlobalRobotDataManager.getInstance().setRosConnected(true);
                LogUtils.i(TAG, "robot 连接成功, 当前线程：" + Thread.currentThread().toString());
                //订阅车辆信息
                mRosService.registerVehicleInfo(mVehicleInfoListener);
                //订阅定位信息
                mRosService.registerLocation(mLocationListener);
                //订阅mqtt信息
                mRosService.registerMqttState(mRobotMqttStateListener);
                //订阅任务信息
//                mRosService.registerTaskInfo(mTaskInfoListener);
            }

            @Override
            public void onDisconnect(boolean normal, @NonNull String reason, int code) {
                isRosConnected = false;
                GlobalRobotDataManager.getInstance().setRosConnected(false);
                LogUtils.i(TAG, "robot onDisconnect===>normal: " + normal + " reason: " + reason + " code: " + code + ", isExist: " + isExist + ", 当前线程：" + Thread.currentThread().toString());
                if(isExist){
                    return;
                }
                //5s后重连
                mHandler.sendEmptyMessageDelayed(ROBOT_RECONNECT_MSG, 5000);
            }

            @Override
            public void onError(@NonNull Exception ex) {
                isRosConnected = false;
                GlobalRobotDataManager.getInstance().setRosConnected(false);
                LogUtils.e(TAG, "robot 连接失败：" + ex.getMessage());
            }
        });
    }

    private AbsMessageListener<VehicleInfo> mVehicleInfoListener = new AbsMessageListener<VehicleInfo>() {
        @Override
        public void onSuccess(VehicleInfo resp) {
            if (resp != null) {
                LogUtils.i(TAG, "车辆信息：" + new Gson().toJson(resp));
                String robotName = GlobalRobotDataManager.getInstance().getRobotSn();
                if(robotName != null && TextUtils.equals(resp.robot_vin, robotName)) {
                    //状态一致，不发送
                    LogUtils.i(TAG, "vehicleInfo 状态一致，不发送");
                    return;
                }
                GlobalRobotDataManager.getInstance().setVehicleInfo(resp);
                EventBus.getDefault().post(new VehicleInfoEvent(resp));
            } else {
                LogUtils.i(TAG, "车辆信息为空");
            }
        }

        @Override
        public void onError(int code, String errorMsg) {
            LogUtils.e(TAG, "获取车辆信息失败：" + errorMsg);
            mHandler.sendEmptyMessageDelayed(ROBOT_GET_VEHICLE_INFO, 3000);
        }
    };

    private AbsMessageListener<MqttState> mRobotMqttStateListener = new AbsMessageListener<MqttState>() {
        @Override
        public void onSuccess(MqttState resp) {
            if (resp != null) {
                LogUtils.i(TAG, "mqtt状态：" + new Gson().toJson(resp));
                MqttMsgEvent event = GlobalRobotDataManager.getInstance().getMqttMsgEvent();
                if (event != null && event.isConnected() == isMqttConnected(resp.connect_state)) {
                    //状态一致，不发送
                    LogUtils.i(TAG, "mqtt状态一致，不发送");
                    return;
                }
                event = new MqttMsgEvent(TextUtils.equals(resp.connect_state, "true"));
                GlobalRobotDataManager.getInstance().setMqttMsgEvent(event);
                EventBus.getDefault().post(event);
            } else {
                LogUtils.i(TAG, "mqtt状态为空");
            }
        }

        @Override
        public void onError(int code, String errorMsg) {
            LogUtils.e(TAG, "获取mqtt状态失败：" + errorMsg);
        }
    };

    private AbsMessageListener<RobotMonitorState> mLocationListener = new AbsMessageListener<RobotMonitorState>() {
        @Override
        public void onSuccess(RobotMonitorState resp) {
            if (resp != null) {
                LogUtils.i(TAG, "监控状态：" + new Gson().toJson(resp));
                MonitorMsgEvent event = GlobalRobotDataManager.getInstance().getMonitorMsgEvent();
                if (event != null && (event.isStatus() == resp.locate_state) && (event.getBattery() == resp.battery)) {
                    //状态一致，不发送
                    LogUtils.i(TAG, "监控状态一致，不发送");

                    return;
                }
                event = new MonitorMsgEvent((int) resp.battery, resp.locate_state);
                GlobalRobotDataManager.getInstance().setMonitorMsgEvent(event);
                EventBus.getDefault().post(event);
            } else {
                LogUtils.i(TAG, "监控状态为空");
            }

        }

        @Override
        public void onError(int code, String errorMsg) {
            LogUtils.e(TAG, "获取监控状态失败：" + errorMsg);
        }
    };


    private AbsMessageListener<TaskInfo> mTaskInfoListener = new AbsMessageListener<TaskInfo>() {
        @Override
        public void onSuccess(TaskInfo resp) {
            if (resp != null) {
                LogUtils.i(TAG, "任务状态：" + new Gson().toJson(resp));
                if (TextUtils.equals(resp.travel_status, Constants.TRAVEL_START)) {
                    TaskInfoStartEvent startEvent = getTaskInfoStart(resp.task_info);
                    if (startEvent != null) {
                        //更新数据
                        GlobalRobotDataManager.getInstance().setTaskStartInfo(null);
                        GlobalRobotDataManager.getInstance().setTaskStartInfo(startEvent);
                        EventBus.getDefault().post(startEvent);
                    }
                } else if (TextUtils.equals(resp.travel_status, Constants.TRAVEL_STOP) || TextUtils.equals(resp.travel_status, Constants.TRAVEL_END)) {
                    TaskInfoEndEvent taskInfoEndEvent = getTaskInfoEnd(resp.task_info);
                    if (taskInfoEndEvent != null) {
                        //更新数据
                        GlobalRobotDataManager.getInstance().setTaskEndInfo(null);
                        GlobalRobotDataManager.getInstance().setTaskEndInfo(taskInfoEndEvent);
                        EventBus.getDefault().post(taskInfoEndEvent);
                    }
                }
            } else {
                LogUtils.i(TAG, "任务状态为空");
            }
        }

        @Override
        public void onError(int code, String errorMsg) {
            LogUtils.e(TAG, "获取任务状态失败：" + errorMsg);
        }
    };


    private TaskInfoEndEvent getTaskInfoEnd(String taskInfoJson) {
        TaskStartInfo taskStartInfo = new Gson().fromJson(taskInfoJson, TaskStartInfo.class);
        LogUtils.i(TAG, "解析任务到达状态信息：" + taskStartInfo.toString());
        if (taskStartInfo != null) {
            TaskInfoEndEvent taskInfoEndEvent = new TaskInfoEndEvent();
            taskInfoEndEvent.setTaskId(taskStartInfo.getTaskId());
            taskInfoEndEvent.setTaskType(taskStartInfo.getTaskType());
            List<PointInfo> pointInfoList = taskStartInfo.getPoints();
            if (pointInfoList != null && pointInfoList.size() > 0) {
                taskInfoEndEvent.setMovingName(pointInfoList.get(0).getMovingName());
            }
            taskInfoEndEvent.setBusinessType(taskStartInfo.getBusinessType());
            taskInfoEndEvent.setLatestOutTime(taskStartInfo.getLatestOutTime());
            taskInfoEndEvent.setShelfType(taskStartInfo.getShelfType());
            taskInfoEndEvent.setShelfNo(taskStartInfo.getShelfNo());
            return taskInfoEndEvent;
        }
        return null;
    }

    private TaskInfoStartEvent getTaskInfoStart(String taskInfoJson) {
        TaskStartInfo taskStartInfo = new Gson().fromJson(taskInfoJson, TaskStartInfo.class);
        LogUtils.i(TAG, "解析任务出发状态信息：" + taskStartInfo.toString());
        if (taskStartInfo != null) {
            TaskInfoStartEvent taskInfoStartEvent = new TaskInfoStartEvent();
            taskInfoStartEvent.setTaskId(taskStartInfo.getTaskId());
            taskInfoStartEvent.setTaskType(taskStartInfo.getTaskType());
            if (taskStartInfo.getPoints() != null && taskStartInfo.getPoints().size() > 0) {
                taskInfoStartEvent.setMovingName(taskStartInfo.getPoints().get(0).getMovingName());
            }
            taskInfoStartEvent.setBusinessType(taskStartInfo.getBusinessType());
            taskInfoStartEvent.setShelfNo(taskStartInfo.getShelfNo());
            taskInfoStartEvent.setLatestOutTime(taskStartInfo.getLatestOutTime());
            return taskInfoStartEvent;
        }
        return null;
    }

    /**
     * 判断mqtt是否连接
     *
     * @param state
     * @return
     */
    private boolean isMqttConnected(String state) {
        return TextUtils.equals(state, "true");
    }


    @Override
    public void onDestroy() {
        isExist = true;
        GlobalRobotDataManager.getInstance().setVehicleInfo(null);
        GlobalRobotDataManager.getInstance().setAlarmInfoList(null);
        GlobalRobotDataManager.getInstance().setMqttConnected(false);
        GlobalRobotDataManager.getInstance().setRosConnected(false);
        GlobalRobotDataManager.getInstance().setMonitorMsgEvent(null);
        GlobalRobotDataManager.getInstance().setTaskStartInfo(null);
        GlobalRobotDataManager.getInstance().setTaskEndInfo(null);
        mRosService.unregisterVehicleInfo();
        mRosService.unregisterBattery();
        mRosService.unregisterLocation();
        mRosService.unregisterTaskInfo();
        mRosService.unregisterMqttState();
        mRosService.disconnect();
        super.onDestroy();
        LogUtils.i(TAG, "RobotRemoteService onDestroy");
    }
}



