package com.jd.amr.hmi.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.amr.hmi.R;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.model.data.event.AlarmDetailInfo;
import com.jd.amr.hmi.model.data.event.AlarmInfoEvent;
import com.jd.amr.hmi.model.data.event.FollowMsgEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoEndEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.mqtt.JdMqttService;
import com.jd.amr.hmi.ui.adapter.ExceptionAdapter;
import com.jd.amr.hmi.ui.dialog.CallDialog;
import com.jd.amr.hmi.ui.widget.GridItemDecoration;
import com.jd.ugv.pad.common.utils.LogUtils;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class ExceptionActivity extends BaseActivity{

    private static final String TAG = "ExceptionActivity";
    private LinearLayout mExcBackLl;
    private RecyclerView mExcRv;
    private TextView mExcCallTv;

    private ExceptionAdapter mExcAdapter;
    private CallDialog mCallDialog;

    @Override
    protected Activity getActivity() {
        return this;
    }

    @Override
    protected int layoutId() {
        return R.layout.activity_exception;
    }

    @Override
    protected void initView() {
        mExcBackLl = findViewById(R.id.exc_back_ll);
        mExcRv = findViewById(R.id.exc_item_rv);
        mExcCallTv = findViewById(R.id.exc_call_person_tv);
        setAdapter();
    }

    @Override
    protected void initListener() {
        mExcBackLl.setOnClickListener(v -> {
            LogUtils.i(TAG, "点击返回");
            finish();
        });
        mExcCallTv.setOnClickListener(v -> {
            LogUtils.i(TAG, "呼叫运维");
            callPerson();
        });
    }

    @Override
    protected void initData() {
        mBaseDraBtn.setVisibility(View.GONE);
//        test();
        List<AlarmDetailInfo> list = GlobalRobotDataManager.getInstance().getAlarmInfoList();
        updateAlarmInfo(list);

    }

    private void setAdapter(){
        mExcAdapter = new ExceptionAdapter();
        mExcRv.setLayoutManager(new GridLayoutManager(this, 2));
        mExcRv.addItemDecoration(new GridItemDecoration(2, 24, 24));
        mExcRv.setAdapter(mExcAdapter);
    }

    private void test(){
        List<AlarmDetailInfo> list = new ArrayList<>();
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
//        list.add(new AlarmDetailInfo(0, "001","异常1", "解决方案1", new Date()));
        List<AlarmDetailInfo> list2 = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            for(int i = 0; i < list.size(); i++){
                list.get(i).setSort(i+1);
            }
        }
        mExcAdapter.setList(list);
    }

    private void updateAlarmInfo(List<AlarmDetailInfo> list) {
        if(list != null){
            mExcAdapter.setList(list);
        }
    }

    private void callPerson() {
        String followUser = GlobalRobotDataManager.getInstance().getFollowUser();
        //展示跟进人
        if(followUser != null){
            String msg = String.format(getString(R.string.string_call_content), followUser);
            showCallDialog(msg);
            return;
        }
        // 呼叫运维
        boolean isConnected = GlobalRobotDataManager.getInstance().isMqttConnected();
        LogUtils.i(TAG, "mqtt status: " + isConnected);
        if(isConnected) {
            startService(JdMqttService.ACTION_REMOTE_CALL);
            String notice = getString(R.string.string_call_notice);
            showCallDialog(notice);
        }else{
            startService(JdMqttService.ACTION_START_INIT);
        }
    }

    private void startService(String action){
        Intent intent = new Intent(this, JdMqttService.class);
        intent.setAction(action);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent);
        } else {
            startService(intent);
        }
    }

    private void showCallDialog(String content) {
        if (mCallDialog == null) {
            mCallDialog = new CallDialog(this);
        }
        if(!mCallDialog.isShowing()) {
            mCallDialog.show();
            mCallDialog.setContentMsg(content);
        }
    }

    private void hideCallDialog() {
        if (mCallDialog != null && mCallDialog.isShowing()) {
            mCallDialog.dismiss();
        }
    }

    @Subscribe
    public void onAlarmInfoEvent(AlarmInfoEvent event) {
        if(event != null) {
            LogUtils.i(TAG, "onAlarmInfoEvent: " + event.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    updateAlarmInfo(event.getCurrentAlarm());
                }
            });
        }
    }

    @Subscribe
    public void onFollowEvent(FollowMsgEvent event) {
        if(event != null) {
            LogUtils.i(TAG, "onFollowEvent: " + event.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if(!TextUtils.isEmpty(event.getFollowUser())) {
                        hideCallDialog();
                        String msg = String.format(getString(R.string.string_call_content), event.getFollowUser());
                        showCallDialog(msg);
                    }
                }
            });
        }
    }

    @Subscribe
    public void onTaskEndEvent(TaskInfoEndEvent event){
        if(event != null){
            LogUtils.i(TAG, "onTaskEndEvent===>event: " + event.toString());
            //判断任务类型，进入不同界面
            if(isBindTask(event.getTaskType()) || isPickTask(event.getTaskType()) || isRecheckTask(event.getTaskType())){
                //进入业务到达页面
                enterActivity(PickOwhsActivity.class);
            }else{
                //进入初始化页面
                enterActivity(InitActivity.class);
            }
            finish();
        }
    }

    @Subscribe
    public void onTaskStartEvent(TaskInfoStartEvent event){
        if(event != null){
            LogUtils.i(TAG, "onTaskStartEvent===>event: " + event.toString());
            enterActivity(MovingActivity.class);
            finish();
        }
    }


}
