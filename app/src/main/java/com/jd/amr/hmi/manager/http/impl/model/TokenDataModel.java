package com.jd.amr.hmi.manager.http.impl.model;

import com.google.gson.Gson;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.http.base.AndroidDataModel;
import com.jd.amr.hmi.manager.http.base.ServerObserver;
import com.jd.amr.hmi.manager.http.impl.request.TokenApiRequest;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.RetryWithDelay;
import com.jd.ugv.pad.common.base.TokenInfoData;
import com.jd.ugv.pad.common.base.TokenRequestBody;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import okhttp3.RequestBody;

public class TokenDataModel extends AndroidDataModel {

    private TokenApiRequest tokenApiRequest;

    public TokenDataModel(){
        tokenApiRequest = new TokenApiRequest();
    }

    public void getTokenInfo(String robotSn, String apiKey,  BaseCallBack callBack){
        if(robotSn != null && apiKey != null) {
            TokenRequestBody tokenBody = new TokenRequestBody(robotSn, apiKey);
            RequestBody body = RequestBody.create(okhttp3.MediaType.parse("application/json;charset=UTF-8"), new Gson().toJson(tokenBody));
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put(Constants.LOP_DN, BuildConfig.LOP_DN);
            Observable<BaseResult<TokenInfoData>> observable = Observable.defer(() -> tokenApiRequest.getTokenData(headerMap, body));
            request(observable, new ServerObserver<BaseResult<TokenInfoData>>() {
                @Override
                public void onComplete(BaseResult<TokenInfoData> tokenDataBaseResult) {
                    if(callBack != null){
                        callBack.success(tokenDataBaseResult);
                    }
                }

                @Override
                public void onFailOrError(BaseResult<TokenInfoData> tokenDataBaseResult, String message) {
                    if(callBack != null){
                        callBack.failOrError(tokenDataBaseResult, message);
                    }
                }

                @Override
                public void onError(String code, String message) {
                    if(callBack != null){
                        callBack.error(code + "," + message);
                    }
                }
            }, new RetryWithDelay(3, 30 * 1000));

        }
    }
}
