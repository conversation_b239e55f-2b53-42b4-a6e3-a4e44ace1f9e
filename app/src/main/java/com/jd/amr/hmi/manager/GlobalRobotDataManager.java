package com.jd.amr.hmi.manager;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;

import com.iscas.rcljava.entity.topic.VehicleInfo;
import com.jd.amr.hmi.Amr;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.model.data.entity.OtaTaskStatusInfo;
import com.jd.amr.hmi.model.data.event.AlarmDetailInfo;
import com.jd.amr.hmi.model.data.event.MonitorMsgEvent;
import com.jd.amr.hmi.model.data.event.MqttMsgEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoEndEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.mqtt.JdMqttService;
import com.jd.ugv.pad.common.base.TokenData;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.SharedPreferencesUtil;

import java.util.List;

/**
 * 机器人数据缓存类
 */
public class GlobalRobotDataManager {

    private static final String TAG = "GlobalRobotDataManager";
    private volatile static GlobalRobotDataManager instance;

    private TokenData tokenData;

    private VehicleInfo vehicleInfo;


    //与云端的mqtt
    private boolean mqttConnected;

    //与本体的socket连接状态
    private boolean isRosConnected;

    private MqttMsgEvent mqttMsgEvent;

    private MonitorMsgEvent monitorMsgEvent;


    private TaskInfoEndEvent mTaskEndInfo;

    private TaskInfoStartEvent mTaskStartInfo;

    private List<AlarmDetailInfo> mAlarmInfoList;

    private String mFollowUser;

    private OtaTaskStatusInfo mOtaTaskStatusInfo = SharedPreferencesUtil.getInstance().getObject(Constants.OTA_TASK_INFO, OtaTaskStatusInfo.class);

    private Intent mJdMqttService;

    public static GlobalRobotDataManager getInstance(){
        if(instance == null){
            synchronized (GlobalRobotDataManager.class){
                if(instance == null){
                    instance = new GlobalRobotDataManager();
                }
            }
        }
        return instance;
    }


    public TokenData getTokenData() {
        return tokenData;
    }

    public void setTokenData(TokenData tokenData) {
        this.tokenData = tokenData;
    }

    public VehicleInfo getVehicleInfo() {
        return vehicleInfo;
    }

    public void setVehicleInfo(VehicleInfo vehicleInfo) {
        this.vehicleInfo = vehicleInfo;
    }

    public String getRobotSn(){
        return vehicleInfo != null ? vehicleInfo.robot_vin : null;
    }

    public String getApiKey(){
        return vehicleInfo != null ? vehicleInfo.client_passwd : null;
    }

    public boolean isMqttConnected() {
        return mqttConnected;
    }

    public void setMqttConnected(boolean mqttConnected) {
        this.mqttConnected = mqttConnected;
    }

    public boolean isRosConnected() {
        return isRosConnected;
    }

    public void setRosConnected(boolean rosConnected) {
        isRosConnected = rosConnected;
    }

    public TaskInfoEndEvent getTaskEndInfo() {
        return mTaskEndInfo;
    }

    public void setTaskEndInfo(TaskInfoEndEvent mTaskEndInfo) {
        this.mTaskEndInfo = mTaskEndInfo;
    }

    public TaskInfoStartEvent getTaskStartInfo() {
        return mTaskStartInfo;
    }

    public void setTaskStartInfo(TaskInfoStartEvent mTaskStartInfo) {
        this.mTaskStartInfo = mTaskStartInfo;
    }

    public List<AlarmDetailInfo> getAlarmInfoList() {
        return mAlarmInfoList;
    }

    public void setAlarmInfoList(List<AlarmDetailInfo> mAlarmInfoList) {
        this.mAlarmInfoList = mAlarmInfoList;
    }

    public String getFollowUser() {
        return mFollowUser;
    }

    public void setFollowUser(String mFollowUser) {
        this.mFollowUser = mFollowUser;
    }

    public MqttMsgEvent getMqttMsgEvent() {
        return mqttMsgEvent;
    }

    public void setMqttMsgEvent(MqttMsgEvent mqttMsgEvent) {
        this.mqttMsgEvent = mqttMsgEvent;
    }

    public MonitorMsgEvent getMonitorMsgEvent() {
        return monitorMsgEvent;
    }

    public void setMonitorMsgEvent(MonitorMsgEvent monitorMsgEvent) {
        this.monitorMsgEvent = monitorMsgEvent;
    }

    public OtaTaskStatusInfo getOtaTaskStatusInfo() {
        return mOtaTaskStatusInfo;
    }

    public void setOtaTaskStatusInfo(OtaTaskStatusInfo mOtaTaskStatusInfo) {
        this.mOtaTaskStatusInfo = mOtaTaskStatusInfo;
        SharedPreferencesUtil.getInstance().saveObject(Constants.OTA_TASK_INFO, mOtaTaskStatusInfo);
    }

    /**
     * 更新ota任务状态
     * @param otaTaskStatusInfo
     * @param status
     * @param remark
     */
    public void updateOtaTaskStatus(OtaTaskStatusInfo otaTaskStatusInfo, int status, String remark) {
        if(mJdMqttService == null){
            mJdMqttService = new Intent(Amr.getInstance(), JdMqttService.class);
        }
        if(otaTaskStatusInfo != null) {
            otaTaskStatusInfo.setStatus(status);
            otaTaskStatusInfo.setRemark(remark);
            otaTaskStatusInfo.setTimestamp(System.currentTimeMillis());
            Bundle bundle = new Bundle();
            bundle.putSerializable(Constants.KEY_OTA_STATUS, otaTaskStatusInfo);
            mJdMqttService.putExtras(bundle);
            mJdMqttService.setAction(JdMqttService.ACTION_OTA_TASK_STATUS);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Amr.getInstance().startForegroundService(mJdMqttService);
            } else {
                Amr.getInstance().startService(mJdMqttService);
            }
        }else{
            LogUtils.e(TAG, "更新ota任务失败");
        }
    }

}

