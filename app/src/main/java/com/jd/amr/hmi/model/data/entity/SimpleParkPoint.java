package com.jd.amr.hmi.model.data.entity;

import java.io.Serializable;

/**
 * 简化版停靠点实体类
 * 用于表示机器人的停靠点基本信息
 */
public class SimpleParkPoint implements Serializable {

    /**
     * 停靠点ID
     */
    private String parkPointId;
    
    /**
     * 停靠点名称
     */
    private String parkPointName;

    /**
     * 默认构造函数
     */
    public SimpleParkPoint() {
    }

    /**
     * 全参数构造函数
     * 
     * @param parkPointId 停靠点ID
     * @param parkPointName 停靠点名称
     */
    public SimpleParkPoint(String parkPointId, String parkPointName) {
        this.parkPointId = parkPointId;
        this.parkPointName = parkPointName;
    }

    /**
     * 获取停靠点ID
     * 
     * @return 停靠点ID
     */
    public String getParkPointId() {
        return parkPointId;
    }

    /**
     * 设置停靠点ID
     * 
     * @param parkPointId 停靠点ID
     */
    public void setParkPointId(String parkPointId) {
        this.parkPointId = parkPointId;
    }

    /**
     * 获取停靠点名称
     * 
     * @return 停靠点名称
     */
    public String getParkPointName() {
        return parkPointName;
    }

    /**
     * 设置停靠点名称
     * 
     * @param parkPointName 停靠点名称
     */
    public void setParkPointName(String parkPointName) {
        this.parkPointName = parkPointName;
    }

    @Override
    public String toString() {
        return "SimpleParkPoint{" +
                "parkPointId='" + parkPointId + '\'' +
                ", parkPointName='" + parkPointName + '\'' +
                '}';
    }
}
