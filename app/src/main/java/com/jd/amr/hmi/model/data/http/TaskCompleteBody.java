package com.jd.amr.hmi.model.data.http;

import com.google.gson.Gson;

public class TaskCompleteBody extends VehicleRequestBody{

    /**
     * 子任务id
     */
    private String subTaskId;

    public TaskCompleteBody(String robotSn, String subTaskId) {
        super(robotSn);
        this.subTaskId = subTaskId;
    }


    @Override
    public String makeRequestBody() {
        return new Gson().toJson(this);
    }
}
