package com.jd.amr.hmi.manager.http.impl.model;

import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.base.AndroidDataModel;
import com.jd.amr.hmi.manager.http.base.AndroidResponse;
import com.jd.amr.hmi.manager.http.base.ServerObserver;
import com.jd.amr.hmi.manager.http.impl.request.TaskApiRequest;
import com.jd.amr.hmi.model.data.entity.ParkPointListResponse;
import com.jd.amr.hmi.model.data.entity.TaskInfo;
import com.jd.amr.hmi.model.data.entity.OssUploadPreSignedUrlResponse;
import com.jd.amr.hmi.model.data.http.OssUploadPreSignedUrlRequestBody;
import com.jd.amr.hmi.model.data.http.ShelfRequestBody;
import com.jd.amr.hmi.model.data.http.TaskCompleteBody;
import com.jd.amr.hmi.model.data.http.VehicleRequestBody;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;

import io.reactivex.rxjava3.core.Observable;

public class TaskDataModel extends AndroidDataModel {

    private TaskApiRequest taskApiRequest;

    public TaskDataModel(){
        taskApiRequest = new TaskApiRequest();
    }

    /**
     * 绑定上装
     * @param shelfRequestBody 请求体
     * @param callback
     */
    public void bindShelfNo(ShelfRequestBody shelfRequestBody, BaseCallBack callback){
        Observable<BaseResult<AndroidResponse>> observable =
                Observable.defer(() -> taskApiRequest.bindShelfRequest(shelfRequestBody));
        request(observable, new ServerObserver<BaseResult<AndroidResponse>>() {
            @Override
            public void onComplete(BaseResult<AndroidResponse> result) {
                callback.success(result);
                callback.afterWork();
            }

            @Override
            public void onFailOrError(BaseResult<AndroidResponse> baseResponseBaseResult, String message) {
                callback.failOrError(baseResponseBaseResult, message);
                callback.afterWork();
            }

            @Override
            public void onError(String code, String message) {
                callback.error(code + "," + message);
                callback.afterWork();
            }
        });
    }

    /**
     * 上报任务完成
     * @param taskCompleteBody 请求体
     * @param callback 回调
     */
    public void taskComplete(TaskCompleteBody taskCompleteBody, BaseCallBack callback) {
        Observable<BaseResult<AndroidResponse>> observable = Observable.defer(() -> taskApiRequest.taskCompleteRequest(taskCompleteBody));
        request(observable, new ServerObserver<BaseResult<AndroidResponse>>() {
            @Override
            public void onComplete(BaseResult<AndroidResponse> androidResponseBaseResult) {
                callback.success(androidResponseBaseResult);
                callback.afterWork();
            }

            @Override
            public void onFailOrError(BaseResult<AndroidResponse> androidResponseBaseResult, String message) {
                callback.failOrError(androidResponseBaseResult, message);
                callback.afterWork();
            }

            @Override
            public void onError(String code, String message) {
                callback.error(code + "," + message);
                callback.afterWork();
            }
        });
    }

    /**
     * 获取任务信息
     * @param callback 回调
     */
    public void getTaskInfo(BaseCallBack<TaskInfo> callback) {
        VehicleRequestBody vehicleRequestBody = new VehicleRequestBody(GlobalRobotDataManager.getInstance().getRobotSn());
        Observable<BaseResult<TaskInfo>> observable = Observable.defer(() -> taskApiRequest.getTaskInfoRequest(vehicleRequestBody));
        request(observable, new ServerObserver<BaseResult<TaskInfo>>() {
            @Override
            public void onComplete(BaseResult<TaskInfo> taskInfoBaseResult) {
                callback.success(taskInfoBaseResult);
                callback.afterWork();
            }

            @Override
            public void onFailOrError(BaseResult<TaskInfo> taskInfoBaseResult, String message) {
                callback.failOrError(taskInfoBaseResult, message);
                callback.afterWork();
            }

            @Override
            public void onError(String code, String message) {
                callback.error(code + "," + message);
                callback.afterWork();
            }
        });
    }

    /**
     * 获取地图点位信息
     * @param callback 回调
     */
    public void getPointList(BaseCallBack<ParkPointListResponse> callback) {
        VehicleRequestBody vehicleRequestBody = new VehicleRequestBody(GlobalRobotDataManager.getInstance().getRobotSn());
        Observable<BaseResult<ParkPointListResponse>> observable = Observable.defer(() -> taskApiRequest.getPointListRequest(vehicleRequestBody));
        request(observable, new ServerObserver<BaseResult<ParkPointListResponse>>() {
            @Override
            public void onComplete(BaseResult<ParkPointListResponse> pointListBaseResult) {
                callback.success(pointListBaseResult);
                callback.afterWork();
            }

            @Override
            public void onFailOrError(BaseResult<ParkPointListResponse> pointListBaseResult, String message) {
                callback.failOrError(pointListBaseResult, message);
                callback.afterWork();
            }

            @Override
            public void onError(String code, String message) {
                callback.error(code + "," + message);
                callback.afterWork();
            }
        });
    }

    /**
     * 获取OSS上传预签名地址
     * @param request OSS上传预签名地址请求体
     * @param callback 回调
     */
    public void getOssUploadPreSignedUrl(OssUploadPreSignedUrlRequestBody request, BaseCallBack<OssUploadPreSignedUrlResponse> callback) {
        Observable<BaseResult<OssUploadPreSignedUrlResponse>> observable = Observable.defer(() -> taskApiRequest.getOssUploadPreSignedUrlRequest(request));
        request(observable, new ServerObserver<BaseResult<OssUploadPreSignedUrlResponse>>() {
            @Override
            public void onComplete(BaseResult<OssUploadPreSignedUrlResponse> result) {
                callback.success(result);
                callback.afterWork();
            }

            @Override
            public void onFailOrError(BaseResult<OssUploadPreSignedUrlResponse> result, String message) {
                callback.failOrError(result, message);
                callback.afterWork();
            }

            @Override
            public void onError(String code, String message) {
                callback.error(code + "," + message);
                callback.afterWork();
            }
        });
    }
}
