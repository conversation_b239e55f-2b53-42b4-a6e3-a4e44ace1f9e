package com.jd.amr.hmi.ui.activity;


import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.iscas.rcljava.entity.service.TowingResult;
import com.iscas.rcljava.robot.AbsMessageListener;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.R;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.impl.model.TaskDataModel;
import com.jd.amr.hmi.manager.http.impl.model.TokenDataModel;
import com.jd.amr.hmi.manager.socket.IRosService;
import com.jd.amr.hmi.manager.socket.impl.RosServiceImpl;
import com.jd.amr.hmi.model.data.event.TaskInfoEndEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.model.data.http.ShelfRequestBody;
import com.jd.amr.hmi.model.data.http.TaskCompleteBody;
import com.jd.amr.hmi.ui.widget.BindToast;
import com.jd.amr.hmi.util.Scanner;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.TokenData;
import com.jd.ugv.pad.common.base.TokenInfoData;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.StringUtils;

import org.greenrobot.eventbus.Subscribe;


public class PickOwhsActivity extends BaseActivity{

    private static final String TAG = "PickOwhsActivity";

    private TextView mPickLatestTimeTv;
    private ConstraintLayout mPickWaitBindCl;
    private TextView mPickWaitBindTitleTv;
    private LinearLayout mPickWaitScanFrameNoLl;
    private EditText mPickWaitScanFrameNoEt;

    private ConstraintLayout mPickPickCl;
    private TextView mPickPickFrameNoTv;

    private ConstraintLayout mPickUnloadCl;
    private TextView mPickUnloadFrameNoTv;

    private ImageView mPickObImg;
    private ImageView mPickObFlowImg;

    private ImageView mPickUpDownLineImg;
    private LinearLayout mPickRobotUpDownLl;
    private LinearLayout mPickRobotUpLl;
    private ImageView mPickRobotUpImg;
    private TextView mPickRobotUpTv;
    private LinearLayout mPickRobotDownLl;
    private ImageView mPickRobotDownImg;
    private TextView mPickRobotDownTv;
    private AppCompatButton mPickConfirmBtn;

    private TaskDataModel mTaskDataModel;
    private TokenDataModel mTokenDataModel;
    private Scanner mScanner;
    private IRosService mRosService;

    private static final String TYPE_BIND = "bind_shelf";
    private static final String TYPE_TASK_COMPLETE = "task_complete";

    @Override
    protected Activity getActivity() {
        return this;
    }

    @Override
    protected int layoutId() {
        return R.layout.activity_pick_owhs;
    }

    @Override
    protected void initView() {
        mPickLatestTimeTv = findViewById(R.id.pick_latest_time_tv);
        mPickWaitBindCl = findViewById(R.id.pick_wait_bind_cl);
        mPickWaitBindTitleTv = findViewById(R.id.pick_wait_bind_no_tv);
        mPickWaitScanFrameNoLl = findViewById(R.id.pick_wait_scan_serial_ll);
        mPickWaitScanFrameNoEt = findViewById(R.id.pick_wait_scan_serial_et);
        mPickPickCl = findViewById(R.id.pick_pick_cl);
        mPickPickFrameNoTv = findViewById(R.id.pick_pick_tv);
        mPickUnloadCl = findViewById(R.id.pick_unload_cl);
        mPickUnloadFrameNoTv = findViewById(R.id.pick_unload_sn_title_tv);
        mPickObImg = findViewById(R.id.pick_owhs_img);
        mPickObFlowImg = findViewById(R.id.pick_pick_owhs_img);
        mPickUpDownLineImg = findViewById(R.id.pick_up_down_line_img);
        mPickRobotUpDownLl = findViewById(R.id.pick_robot_up_down_ll);
        mPickRobotUpLl = findViewById(R.id.pick_robot_up_ll);
        mPickRobotUpImg = findViewById(R.id.pick_robot_up_img);
        mPickRobotUpTv = findViewById(R.id.pick_robot_up_tv);
        mPickRobotDownLl = findViewById(R.id.pick_robot_down_ll);
        mPickRobotDownImg = findViewById(R.id.pick_robot_down_img);
        mPickRobotDownTv = findViewById(R.id.pick_robot_down_tv);
        mPickConfirmBtn = findViewById(R.id.pick_scan_confirm_btn);
    }

    @Override
    protected void initListener() {
        mPickRobotUpLl.setOnClickListener(v -> {
            //上升立柱
            sendRobotTowingAction(1);
        });
        mPickRobotDownLl.setOnClickListener(v -> {
            //下降立柱
            sendRobotTowingAction(2);
            //3s后上报任务完成
            mPickRobotDownLl.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if(hasToken()) {
                        reportTaskComplete();
                    }else {
                        getTokenInfo(TYPE_TASK_COMPLETE, null);
                    }
                }
            }, 3000);
        });
        mScanner.setScanListener(new Scanner.ScanListener() {
            @Override
            public void onScanBegin() {
            }

            @Override
            public void onScanFinish(String code) {
                mPickWaitScanFrameNoEt.setText(code);
                mPickWaitScanFrameNoEt.setSelection(code.length());
                //扫码完成，直接确认
                if(hasToken()) {
                    checkShelfNo(code);
                }else{
                    getTokenInfo(TYPE_BIND, code);
                }
            }
        });


        mPickWaitScanFrameNoEt.setOnKeyListener((v, keyCode, event) -> {
            return mScanner.handleScanCode(event); // 返回 true 表示已处理该事件
        });

        mPickConfirmBtn.setOnClickListener(v -> {
            String num = mPickWaitScanFrameNoEt.getText().toString();
            if(hasToken()) {
                checkShelfNo(num);
            }else{
                getTokenInfo(TYPE_BIND, num);
            }
        });
    }

    @Override
    protected void initData() {
        mScanner = new Scanner();
        mRosService = new RosServiceImpl();
        TaskInfoEndEvent taskInfoEndEvent = GlobalRobotDataManager.getInstance().getTaskEndInfo();
        if(taskInfoEndEvent != null){
            //已到达
            handleArrivedView(taskInfoEndEvent);
        }
    }

    private void handleArrivedView(TaskInfoEndEvent taskInfoEndEvent){
        if(taskInfoEndEvent != null){
            //展示底部停靠信息
            mBaseBottomLocImg.setImageResource(R.mipmap.icon_location);
            mBaseBottomStopTitleTv.setText(R.string.string_robot_arrived);
            mBaseBottomStopNameTv.setText(taskInfoEndEvent.getMovingName());

            //设置时间图片信息
            mPickLatestTimeTv.setText(StringUtils.getDateTime(taskInfoEndEvent.getLatestOutTime()));
            mPickObFlowImg.setVisibility(isObFlowBusiness(taskInfoEndEvent.getBusinessType())  ? View.VISIBLE : View.GONE);
            mPickObImg.setVisibility(isObBusiness(taskInfoEndEvent.getBusinessType())  ? View.VISIBLE : View.GONE);

            LogUtils.i(TAG, "taskType: " + taskInfoEndEvent.getTaskType());
            //展示任务界面
            if(isBindTask(taskInfoEndEvent.getTaskType())){
                LogUtils.i(TAG, "isBindTask");
                //展示绑箱任务界面
                mPickWaitBindTitleTv.setText("请绑定" + taskInfoEndEvent.getShelfType() + "上装");
                showWaitBindCl();
                showRobotUpDownView(taskInfoEndEvent.getTaskType());
            }else if(isPickTask(taskInfoEndEvent.getTaskType())){
                //展示拣货任务界面
                mPickPickFrameNoTv.setText(String.format(getString(R.string.string_vehicle_serial), taskInfoEndEvent.getShelfNo()));
                showPickCl();
            }else if(isRecheckTask(taskInfoEndEvent.getTaskType())){
                //展示复核任务界面
                mPickUnloadFrameNoTv.setText(String.format(getString(R.string.string_vehicle_serial), taskInfoEndEvent.getShelfNo()));
                showUnloadCl();
                showRobotUpDownView(taskInfoEndEvent.getTaskType());
            }
        }
    }

    private void showRobotUpDownView(String type) {
        mPickRobotUpDownLl.setVisibility(View.VISIBLE);
        mPickUpDownLineImg.setVisibility(View.VISIBLE);
        if(isBindTask(type)){
            //默认展示上升按钮
            showClickUpView();
        }else if(isRecheckTask(type)){
            //默认展示下降按钮
            showClickDownView();
        }
    }

    private void showClickUpView(){
        mPickRobotUpLl.setBackgroundResource(R.drawable.bg_robot_upright);
        mPickRobotUpImg.setImageResource(R.mipmap.icon_robot_up_white);
        mPickRobotUpTv.setTextColor(getColor(R.color.color_white));
        mPickRobotDownLl.setBackgroundResource(0);
        mPickRobotDownImg.setImageResource(R.mipmap.icon_robot_down_black);
        mPickRobotDownTv.setTextColor(getColor(R.color.black_525765));

    }

    private void showClickDownView() {
        mPickRobotDownLl.setBackgroundResource(R.drawable.bg_robot_upright);
        mPickRobotDownImg.setImageResource(R.mipmap.icon_robot_down_white);
        mPickRobotDownTv.setTextColor(getColor(R.color.color_white));
        mPickRobotUpLl.setBackgroundResource(0);
        mPickRobotUpImg.setImageResource(R.mipmap.icon_robot_up_black);
        mPickRobotUpTv.setTextColor(getColor(R.color.black_525765));
    }

    private void showWaitBindCl(){
        mPickConfirmBtn.setVisibility(View.VISIBLE);
        mPickWaitBindCl.setVisibility(View.VISIBLE);
        mPickPickCl.setVisibility(View.GONE);
        mPickUnloadCl.setVisibility(View.GONE);
    }

    private void showPickCl(){
        mPickConfirmBtn.setVisibility(View.GONE);
        mPickPickCl.setVisibility(View.VISIBLE);
        mPickWaitBindCl.setVisibility(View.GONE);
        mPickUnloadCl.setVisibility(View.GONE);
    }

    private void showUnloadCl() {
        mPickConfirmBtn.setVisibility(View.GONE);
        mPickUnloadCl.setVisibility(View.VISIBLE);
        mPickWaitBindCl.setVisibility(View.GONE);
        mPickPickCl.setVisibility(View.GONE);
    }

    private void sendRobotTowingAction(int type){
        if(mRosService == null) {
            mRosService = new RosServiceImpl();
        }
        mRosService.robotTowingAction(type, new AbsMessageListener<TowingResult>() {
            @Override
            public void onSuccess(TowingResult resp) {
                LogUtils.i(TAG, "sendRobotTowingAction success");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if(type == 1) {
                            showClickUpView();
                        }else{
                            showClickDownView();
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String errorMsg) {
                LogUtils.i(TAG, "sendRobotTowingAction error code:" + code + " errorMsg:" + errorMsg);
            }
        });
    }

    private void getTokenInfo(String type, String shelfNo){
        if(mTokenDataModel == null){
            mTokenDataModel = new TokenDataModel();
        }
        String robotName = GlobalRobotDataManager.getInstance().getRobotSn();
        String apiKey = GlobalRobotDataManager.getInstance().getApiKey();
        LogUtils.i(TAG, "getTokenInfo===>robotName: " + robotName + ", apiKey: " + apiKey);
        if(robotName != null && apiKey != null){
            mTokenDataModel.getTokenInfo(robotName, apiKey, new BaseCallBack() {
                @Override
                public void success(BaseResult baseResult) {
                    TokenInfoData tokenInfoData = (TokenInfoData) baseResult.getData();
                    if (tokenInfoData != null) {
                        TokenData tokenData = new TokenData(tokenInfoData.getAccessToken(), robotName, tokenInfoData.getExpiresIn(), BuildConfig.LOP_DN);
                        tokenData.setRobotSn(robotName);
                        tokenData.setApkKey(apiKey);
                        tokenData.setUrl(BuildConfig.PROXY_URL + Constants.TOKEN);
                        GlobalRobotDataManager.getInstance().setTokenData(tokenData);
                        if(TextUtils.equals(type, TYPE_BIND)) {
                            checkShelfNo(shelfNo);
                        }else if(TextUtils.equals(type, TYPE_TASK_COMPLETE)){
                            reportTaskComplete();
                        }
                    }
                }

                @Override
                public boolean failOrError(BaseResult baseResult, String e) {
                    LogUtils.e(TAG, "getTokenInfo failOrError: " + e);
                    BindToast.showToastWithImg("请求失败，请检查网络");
                    return false;
                }
            });
        }
    }

    private void checkShelfNo(String shelfNo){
        if(mTaskDataModel == null){
            mTaskDataModel = new TaskDataModel();
        }
        ShelfRequestBody shelfRequestBody = new ShelfRequestBody(GlobalRobotDataManager.getInstance().getRobotSn(), shelfNo);
        LogUtils.i(TAG, "checkShelfNo===>confirm enable: " + mPickConfirmBtn.isEnabled());
        //防止重复点击
        mPickConfirmBtn.setEnabled(false);
        mTaskDataModel.bindShelfNo(shelfRequestBody, new BaseCallBack() {
            @Override
            public void success(BaseResult baseResult) {
                LogUtils.i(TAG, "上装绑定成功");
                mPickConfirmBtn.setEnabled(true);
            }

            @Override
            public boolean failOrError(BaseResult baseResult, String e) {
                LogUtils.i(TAG, "上装绑定失败: " + e);
                BindToast.showToastWithImg(e);
                showBindShelfError(e);
                mPickConfirmBtn.setEnabled(true);
                return true;
            }

            @Override
            public void error(String e) {
                BaseCallBack.super.error(e);
                mPickConfirmBtn.setEnabled(true);
            }
        });
    }


    private void reportTaskComplete(){
        if(mTaskDataModel == null){
            mTaskDataModel = new TaskDataModel();
        }
        String taskId = null;
        TaskInfoEndEvent taskInfoEndEvent = GlobalRobotDataManager.getInstance().getTaskEndInfo();
        if(taskInfoEndEvent != null){
            taskId = taskInfoEndEvent.getTaskId();
        }
        if(!isRecheckTask(taskInfoEndEvent.getTaskType())){
            LogUtils.i(TAG, "当前不是复合任务，不能上报任务完成");
            return;
        }
        TaskCompleteBody taskCompleteBody = new TaskCompleteBody(GlobalRobotDataManager.getInstance().getRobotSn(), taskId);
        mTaskDataModel.taskComplete(taskCompleteBody, new BaseCallBack() {
            public void success(BaseResult baseResult) {
                LogUtils.i(TAG, "上报任务完成成功");
            }

            @Override
            public boolean failOrError(BaseResult baseResult, String e) {
                LogUtils.i(TAG, "上报任务完成失败: " + e);
                return true;
            }
        });
    }

    private void showBindShelfError(String errorMsg){
        mPickWaitScanFrameNoLl.setBackgroundResource(R.drawable.bg_scan_serial_num_error);
        mPickWaitScanFrameNoEt.selectAll();
        mPickWaitScanFrameNoEt.postDelayed(new Runnable() {
            @Override
            public void run() {
                //2s后清空输入框
                mPickWaitScanFrameNoEt.setText("");
                mPickWaitScanFrameNoLl.setBackgroundResource(R.drawable.bg_scan_serial_num);
            }
        }, 2000);
    }

    @Subscribe
    public void onTaskStartEvent(TaskInfoStartEvent event){
        if(event != null){
            LogUtils.i(TAG, "onTaskStartEvent===>event: " + event.toString());
            enterActivity(MovingActivity.class);
            finish();
        }
    }

}
