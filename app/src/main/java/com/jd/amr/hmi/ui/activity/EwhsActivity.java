package com.jd.amr.hmi.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.iscas.rcljava.entity.service.TowingResult;
import com.iscas.rcljava.robot.AbsMessageListener;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.R;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.impl.model.TaskDataModel;
import com.jd.amr.hmi.manager.http.impl.model.TokenDataModel;
import com.jd.amr.hmi.manager.socket.IRosService;
import com.jd.amr.hmi.manager.socket.impl.RosServiceImpl;
import com.jd.amr.hmi.model.data.event.TaskInfoEndEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.model.data.http.ShelfRequestBody;
import com.jd.amr.hmi.model.data.http.TaskCompleteBody;
import com.jd.amr.hmi.util.Scanner;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.TokenData;
import com.jd.ugv.pad.common.base.TokenInfoData;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.StringUtils;

import org.greenrobot.eventbus.Subscribe;

public class EwhsActivity extends BaseActivity {

    private static final String TAG = "EwhsActivity";
    private static final String TYPE_BIND = "bind_shelf";
    private static final String TYPE_TASK_COMPLETE = "task_complete";

    // 视图组件
    private TextView mEwhsLatestTimeTv;
    private ConstraintLayout mEwhsWaitBindCl;
    private TextView mEwhsWaitBindTitleTv;
    private LinearLayout mEwhsWaitScanFrameNoLl;
    private EditText mEwhsWaitScanFrameNoEt;
    private ConstraintLayout mEwhsUnloadCl;
    private TextView mEwhsUnloadSnTitleTv;
    private ImageView mEwhsUpDownLineImg;
    private LinearLayout mEwhsRobotUpDownLl;
    private LinearLayout mEwhsRobotUpLl;
    private ImageView mEwhsRobotUpImg;
    private TextView mEwhsRobotUpTv;
    private LinearLayout mEwhsRobotDownLl;
    private ImageView mEwhsRobotDownImg;
    private TextView mEwhsRobotDownTv;
    private AppCompatButton mEwhsConfirmBtn;
    private RecyclerView mEwhsGoodsGridRv;

    private ConstraintLayout mEwhsContentCl;
    private ImageView mEwhsLockImg;
    private TextView mEwhsWaitBindNoTv;
    private TextView mEwhsWaitBindSerialTv;
    private ImageView mEwhsUnloadLockImg;
    private TextView mEwhsUnloadArrivedTv;
    private TextView mEwhsUnloadTitleTv;
    private ConstraintLayout mEwhsUnloadGoodsCl;
    private RecyclerView mEwhsUnloadGoodsRv;

    // 业务组件
    private TaskDataModel mTaskDataModel;
    private TokenDataModel mTokenDataModel;
    private Scanner mScanner;
    private IRosService mRosService;

    @Override
    protected Activity getActivity() {
        return this;
    }

    @Override
    protected int layoutId() {
        return R.layout.activity_ewhs;
    }

    @Override
    protected void initView() {
        // 初始化视图组件
        mEwhsLatestTimeTv = findViewById(R.id.ewhs_latest_time_tv);
        mEwhsWaitBindCl = findViewById(R.id.ewhs_wait_bind_cl);
        mEwhsWaitBindTitleTv = findViewById(R.id.ewhs_wait_bind_title_tv);
        mEwhsWaitScanFrameNoLl = findViewById(R.id.ewhs_wait_scan_serial_ll);
        mEwhsWaitScanFrameNoEt = findViewById(R.id.ewhs_wait_scan_serial_et);

        mEwhsUnloadCl = findViewById(R.id.ewhs_unload_cl);
        mEwhsUnloadSnTitleTv = findViewById(R.id.ewhs_unload_sn_title_tv);
        mEwhsUpDownLineImg = findViewById(R.id.ewhs_up_down_line_img);
        mEwhsRobotUpDownLl = findViewById(R.id.ewhs_robot_up_down_ll);
        mEwhsRobotUpLl = findViewById(R.id.ewhs_robot_up_ll);
        mEwhsRobotUpImg = findViewById(R.id.ewhs_robot_up_img);
        mEwhsRobotUpTv = findViewById(R.id.ewhs_robot_up_tv);
        mEwhsRobotDownLl = findViewById(R.id.ewhs_robot_down_ll);
        mEwhsRobotDownImg = findViewById(R.id.ewhs_robot_down_img);
        mEwhsRobotDownTv = findViewById(R.id.ewhs_robot_down_tv);
        mEwhsConfirmBtn = findViewById(R.id.ewhs_scan_confirm_btn);
        mEwhsGoodsGridRv = findViewById(R.id.ewhs_goods_grid_rv);

        mEwhsContentCl = findViewById(R.id.ewhs_content_cl);
        mEwhsLockImg = findViewById(R.id.ewhs_lock_img);
        mEwhsWaitBindNoTv = findViewById(R.id.ewhs_wait_bind_no_tv);
        mEwhsWaitBindSerialTv = findViewById(R.id.ewhs_wait_bind_serial_tv);
        mEwhsUnloadLockImg = findViewById(R.id.ewhs_unload_lock_img);
        mEwhsUnloadArrivedTv = findViewById(R.id.ewhs_unload_arrived_tv);
        mEwhsUnloadTitleTv = findViewById(R.id.ewhs_unload_title_tv);
        mEwhsUnloadGoodsCl = findViewById(R.id.ewhs_unload_goods_cl);
        mEwhsUnloadGoodsRv = findViewById(R.id.ewhs_unload_goods_rv);
    }

    @Override
    protected void initListener() {
        // 机器人升降操作
//        mEwhsRobotUpLl.setOnClickListener(v -> handleRobotAction(Constants.ROBOT_ACTION_UP));
//        mEwhsRobotDownLl.setOnClickListener(v -> handleRobotAction(Constants.ROBOT_ACTION_DOWN));

        // 扫码监听
        mScanner.setScanListener(new Scanner.ScanListener() {
            @Override
            public void onScanBegin() { /* 扫码开始处理 */ }

            @Override
            public void onScanFinish(String code) {
                processScannedCode(code);
            }
        });

        // 确认按钮
        mEwhsConfirmBtn.setOnClickListener(v -> validateInputAndProcess());
    }

    @Override
    protected void initData() {
        initializeServices();
        loadTaskData();
    }

    private void initializeServices() {
        mScanner = new Scanner();
        mRosService = new RosServiceImpl();
        mTaskDataModel = new TaskDataModel();
        mTokenDataModel = new TokenDataModel();
    }

    private void loadTaskData() {
        TaskInfoEndEvent taskInfo = GlobalRobotDataManager.getInstance().getTaskEndInfo();
        if (taskInfo != null) {
            updateTaskUI(taskInfo);
        }
    }

    private void updateTaskUI(TaskInfoEndEvent taskInfo) {
        // 更新时间显示
        mEwhsLatestTimeTv.setText(StringUtils.getDateTime(taskInfo.getLatestOutTime()));

        // 根据任务类型显示对应界面
//        switch (taskInfo.getTaskType()) {
//            case Constants.TASK_TYPE_BIND:
//                showBindingInterface(taskInfo);
//                break;
//            case Constants.TASK_TYPE_OPERATION:
//                showOperationInterface(taskInfo);
//                break;
//            case Constants.TASK_TYPE_RECHECK:
//                showRecheckInterface(taskInfo);
//                break;
//        }
    }

    private void showBindingInterface(TaskInfoEndEvent taskInfo) {
        mEwhsWaitBindTitleTv.setText(String.format("请绑定%s上装", taskInfo.getShelfType()));
        setViewVisibility(mEwhsWaitBindCl, View.VISIBLE);
        updateRobotControls(taskInfo.getTaskType());
    }

    private void showOperationInterface(TaskInfoEndEvent taskInfo) {
        mEwhsUnloadSnTitleTv.setText(String.format("货架编号：%s", taskInfo.getShelfNo()));
        setViewVisibility(mEwhsUnloadCl, View.VISIBLE);
    }

    private void showRecheckInterface(TaskInfoEndEvent taskInfo) {
        // 复核任务界面逻辑
        setViewVisibility(mEwhsConfirmBtn, View.GONE);
        updateRobotControls(taskInfo.getTaskType());
    }

    private void updateRobotControls(String taskType) {
        setViewVisibility(mEwhsRobotUpDownLl, View.VISIBLE);
        setViewVisibility(mEwhsUpDownLineImg, View.VISIBLE);

//        if (Constants.TASK_TYPE_BIND.equals(taskType)) {
//            updateButtonState(mEwhsRobotUpLl, true);
//        } else if (Constants.TASK_TYPE_RECHECK.equals(taskType)) {
//            updateButtonState(mEwhsRobotDownLl, true);
//        }
    }

    private void updateButtonState(View button, boolean isActive) {
        if (button == mEwhsRobotUpLl) {
            int bgRes = isActive ? R.drawable.bg_robot_upright : 0;
            int iconRes = isActive ? R.mipmap.icon_robot_up_white : R.mipmap.icon_robot_up_black;
            int textColor = isActive ? R.color.color_white : R.color.black_525765;

            mEwhsRobotUpLl.setBackgroundResource(bgRes);
            mEwhsRobotUpImg.setImageResource(iconRes);
            mEwhsRobotUpTv.setTextColor(getColor(textColor));
        } else if (button == mEwhsRobotDownLl) {
            int bgRes = isActive ? R.drawable.bg_robot_upright : 0;
            int iconRes = isActive ? R.mipmap.icon_robot_down_white : R.mipmap.icon_robot_down_black;
            int textColor = isActive ? R.color.color_white : R.color.black_525765;

            mEwhsRobotDownLl.setBackgroundResource(bgRes);
            mEwhsRobotDownImg.setImageResource(iconRes);
            mEwhsRobotDownTv.setTextColor(getColor(textColor));
        }
    }

    private void handleRobotAction(int actionType) {
        if (mRosService == null) return;

        mRosService.robotTowingAction(actionType, new AbsMessageListener<TowingResult>() {
            @Override
            public void onSuccess(TowingResult resp) {
                runOnUiThread(() -> handleActionSuccess(actionType));
            }

            @Override
            public void onError(int code, String errorMsg) {
                LogUtils.e(TAG, "操作失败 - 代码：" + code + ", 错误信息：" + errorMsg);
//                showErrorToast("操作失败，错误码：" + code);
            }
        });
    }

    private void handleActionSuccess(int actionType) {
//        if (actionType == Constants.ROBOT_ACTION_UP) {
//            updateButtonState(mEwhsRobotUpLl, true);
//            updateButtonState(mEwhsRobotDownLl, false);
//        } else {
//            updateButtonState(mEwhsRobotDownLl, true);
//            updateButtonState(mEwhsRobotUpLl, false);
//            scheduleTaskCompletion();
//        }
    }

    private void scheduleTaskCompletion() {
        mEwhsRobotDownLl.postDelayed(() -> {
            if (hasValidToken()) {
                reportTaskComplete();
            } else {
                fetchToken(TYPE_TASK_COMPLETE, null);
            }
        }, 3000);
    }

    private void processScannedCode(String code) {
        mEwhsWaitScanFrameNoEt.setText(code);
        mEwhsWaitScanFrameNoEt.setSelection(code.length());
        if (hasValidToken()) {
            verifyShelfNumber(code);
        } else {
            fetchToken(TYPE_BIND, code);
        }
    }

    private void validateInputAndProcess() {
        String input = mEwhsWaitScanFrameNoEt.getText().toString().trim();
        if (TextUtils.isEmpty(input)) {
            showInputError("请输入或扫描上装号");
            return;
        }
        if (hasValidToken()) {
            verifyShelfNumber(input);
        } else {
            fetchToken(TYPE_BIND, input);
        }
    }

    private void fetchToken(String type, String shelfNo) {
        String robotName = GlobalRobotDataManager.getInstance().getRobotSn();
        String apiKey = GlobalRobotDataManager.getInstance().getApiKey();

        mTokenDataModel.getTokenInfo(robotName, apiKey, new BaseCallBack() {
            @Override
            public void success(BaseResult baseResult) {
                TokenInfoData tokenInfo = (TokenInfoData) baseResult.getData();
                if (tokenInfo != null) {
                    TokenData tokenData = new TokenData(
                            tokenInfo.getAccessToken(),
                            robotName,
                            tokenInfo.getExpiresIn(),
                            BuildConfig.LOP_DN
                    );
                    GlobalRobotDataManager.getInstance().setTokenData(tokenData);

                    if (TYPE_BIND.equals(type)) {
                        verifyShelfNumber(shelfNo);
                    } else if (TYPE_TASK_COMPLETE.equals(type)) {
                        reportTaskComplete();
                    }
                }
            }

            @Override
            public boolean failOrError(BaseResult baseResult, String e) {
//                showErrorToast("令牌获取失败：" + e);
                return false;
            }
        });
    }

    private void verifyShelfNumber(String shelfNo) {
        ShelfRequestBody requestBody = new ShelfRequestBody(
                GlobalRobotDataManager.getInstance().getRobotSn(),
                shelfNo
        );

        mTaskDataModel.bindShelfNo(requestBody, new BaseCallBack() {
            @Override
            public void success(BaseResult baseResult) {
//                showSuccess("上装绑定成功");
                resetInputField();
            }

            @Override
            public boolean failOrError(BaseResult baseResult, String e) {
                showInputError(e);
                return true;
            }
        });
    }

    // 新增商品网格处理
    private void initGoodsGrid() {
//        mEwhsGoodsGridRv.setLayoutManager(new GridLayoutManager(this, 3));
//        mEwhsGoodsGridRv.setAdapter(new GoodsGridAdapter(getGoodsData()));
    }

    // 动态调整布局参数
    private void adjustLayoutForTablet() {
//        if (isTabletDevice()) {
//            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mEwhsContentCl.getLayoutParams();
//            params.width = (int) (getScreenWidth() * 0.8);
//            params.height = (int) (getScreenHeight() * 0.7);
//        }
    }

    // 增强的异常处理流程
    private void handleRosServiceError(int errorCode) {
        String errorMessage;
        switch (errorCode) {
            case 401:
                errorMessage = "认证失败，请重新登录";
                break;
            case 503:
                errorMessage = "服务不可用，请稍后重试";
                break;
            default:
                errorMessage = "网络连接异常（错误码：" + errorCode + "）";
        }
        showErrorDialog(errorMessage);
    }

    private void showErrorDialog(String message) {
//        new AlertDialog.Builder(this)
//                .setTitle("操作失败")
//                .setMessage(message)
//                .setPositiveButton("重试", (dialog, which) -> retryLastAction())
//                .setNegativeButton("取消", null)
//                .show();
    }

    private void reportTaskComplete() {
        TaskInfoEndEvent taskInfo = GlobalRobotDataManager.getInstance().getTaskEndInfo();
        if (taskInfo == null || !isRecheckTask(taskInfo.getTaskType())) {
//            showErrorToast("无效的任务状态");
            return;
        }

        TaskCompleteBody completeBody = new TaskCompleteBody(
                GlobalRobotDataManager.getInstance().getRobotSn(),
                taskInfo.getTaskId()
        );

        mTaskDataModel.taskComplete(completeBody, new BaseCallBack() {
            @Override
            public void success(BaseResult baseResult) {
                LogUtils.i(TAG, "任务完成上报成功");
            }

            @Override
            public boolean failOrError(BaseResult baseResult, String e) {
//                showErrorToast("任务完成上报失败：" + e);
                return true;
            }
        });
    }

    // 辅助方法
    private boolean hasValidToken() {
        return GlobalRobotDataManager.getInstance().getTokenData() != null;
    }

//    private boolean isRecheckTask(String taskType) {
//        return Constants.TASK_TYPE_RECHECK.equals(taskType);
//    }

    private void setViewVisibility(View view, int visibility) {
        if (view.getVisibility() != visibility) {
            view.setVisibility(visibility);
        }
    }

//    private void showSuccess(String message) {
//        BindToast.showToastWithImg(message, R.mipmap.icon_success);
//    }

//    private void showErrorToast(String message) {
//        runOnUiThread(() -> BindToast.showToastWithImg(message, R.mipmap.icon_error));
//    }

    private void showInputError(String message) {
        mEwhsWaitScanFrameNoLl.setBackgroundResource(R.drawable.bg_scan_serial_num_error);
        mEwhsWaitScanFrameNoEt.postDelayed(this::resetInputField, 2000);
//        showErrorToast(message);
    }

    private void resetInputField() {
        mEwhsWaitScanFrameNoEt.setText("");
        mEwhsWaitScanFrameNoLl.setBackgroundResource(R.drawable.bg_scan_serial_num);
    }

    @Subscribe
    public void onTaskStartEvent(TaskInfoStartEvent event) {
        if (event != null) {
            startActivity(new Intent(this, MovingActivity.class));
            finish();
        }
    }
}