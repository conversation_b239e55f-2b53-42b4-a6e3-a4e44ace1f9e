package com.jd.amr.hmi.model.data.event;

/**
 * 上传日志事件
 */
public class UploadLogEvent {
    private String fileKey;
    private String bucketName;
    private String date;

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Override
    public String toString() {
        return "UploadLogEvent{" +
                "fileKey='" + fileKey + '\'' +
                ", bucketName='" + bucketName + '\'' +
                ", data='" + date + '\'' +
                '}';
    }
}
