package com.jd.amr.hmi.util;

import com.jd.amr.hmi.BuildConfig;

public class MqttTopicUtils {

    public static String getWillTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/will";
    }

    /**
     * 获取远程呼叫设备topic
     * @param robotName
     * @return
     */
    public static String getCallDeviceTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/events";
    }

    /**
     * 获取远程呼叫设备回复topic
     * @param robotName
     * @return
     */
    public static String getCallDeviceReplyTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/events/reply";
    }

    /**
     * 获取用户跟进topic
     * @param robotName
     * @return
     */
    public static String getFollowUserTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/services";
    }

    /**
     * 获取异常topic
     * @param robotName
     * @return
     */
    public static String getExceptionTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/services/events";
    }

    /**
     * 获取回复监控的topic
     * @param robotName
     * @return
     */
    public static String getMonitorReplyTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/services/reply";
    }

    /**
     * 获取上报ota任务topic
     * @param robotName
     * @return
     */
    public static String getReportOtaTaskTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/ota/events";
    }

    /**
     * 获取发送ota任务topic
     * @param robotName
     * @return
     */
    public static String getSendOtaTaskTopic(String robotName){
        return "t/" + BuildConfig.MQTT_ENV + "/integrate/" + robotName + "/hmi/json/ota/services";
    }

}
