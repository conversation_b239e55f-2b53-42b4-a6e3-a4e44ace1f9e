package com.jd.amr.hmi.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatButton;

import com.iscas.rcljava.entity.service.ArrivedResp;
import com.iscas.rcljava.entity.service.CommonResp;
import com.iscas.rcljava.robot.AbsMessageListener;
import com.jd.amr.hmi.R;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.socket.IRosService;
import com.jd.amr.hmi.manager.socket.impl.RosServiceImpl;
import com.jd.amr.hmi.model.data.event.TaskInfoEndEvent;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.StringUtils;

import org.greenrobot.eventbus.Subscribe;

public class MovingActivity extends BaseActivity {

    private static final String TAG = "MovingActivity";

    private LinearLayout mMovingLatestTimeLl;
    private TextView mMovingLatestTimeTv;

    private TextView mMovingStopTypeTv;
    private TextView mMovingStopTv;
    private TextView mMovingFrameNoTv;
    private ImageView mMovingObFlowImg;
    private ImageView mMovingObImg;
    private AppCompatButton mMovingArriveBtn;

    private IRosService mRosService;

    @Override
    protected Activity getActivity() {
        return this;
    }

    @Override
    protected int layoutId() {
        return R.layout.activity_moving;
    }

    @Override
    protected void initView() {
        mMovingLatestTimeLl = findViewById(R.id.moving_latest_time_ll);
        mMovingLatestTimeTv = findViewById(R.id.moving_latest_time_tv);
        mMovingStopTypeTv = findViewById(R.id.moving_stop_type_tv);
        mMovingStopTv = findViewById(R.id.moving_stop_tv);
        mMovingFrameNoTv = findViewById(R.id.moving_serial_tv);
        mMovingObFlowImg = findViewById(R.id.moving_ob_flow_img);
        mMovingObImg = findViewById(R.id.moving_ob_img);
        mMovingArriveBtn = findViewById(R.id.moving_as_arrive_btn);
    }

    @Override
    protected void initListener() {
        mMovingArriveBtn.setOnClickListener(v -> {
            sendArriveCmdToRobot();
        });
    }

    @Override
    protected void initData() {
        TaskInfoStartEvent taskInfoStartEvent = GlobalRobotDataManager.getInstance().getTaskStartInfo();
        if (taskInfoStartEvent != null) {
            handleMovingView(taskInfoStartEvent);
        }
    }

    /**
     * 处理行走中视图
     *
     * @param taskInfoStartEvent
     */
    private void handleMovingView(TaskInfoStartEvent taskInfoStartEvent) {
        //设置底部区域信息
        mBaseBottomLocImg.setImageResource(R.mipmap.icon_moving);
        mBaseBottomStopTitleTv.setText(getString(R.string.string_robot_going));
        mBaseBottomStopNameTv.setText(taskInfoStartEvent.getMovingName());
        //设置时间信息
        if(taskInfoStartEvent.getLatestOutTime() != null){
            mMovingLatestTimeLl.setVisibility(View.VISIBLE);
            mMovingLatestTimeTv.setText(StringUtils.getDateTime(taskInfoStartEvent.getLatestOutTime()));
        }else{
            mMovingLatestTimeLl.setVisibility(View.GONE);
        }
        if (isPickTask(taskInfoStartEvent.getTaskType()) || isRecheckTask(taskInfoStartEvent.getTaskType())) {
            //展示拣选任务的视图
            showMovingPickOrCheckTaskView(taskInfoStartEvent.getMovingName(), taskInfoStartEvent.getShelfNo(), taskInfoStartEvent.getBusinessType());
        }else{
            showMovingCommonTaskView(taskInfoStartEvent.getMovingName());
        }
    }


    private void showMovingCommonTaskView(String stopName) {
        setMovingStopName(stopName);
        mMovingFrameNoTv.setVisibility(View.GONE);
        mMovingObFlowImg.setVisibility(View.GONE);
        mMovingObImg.setVisibility(View.GONE);
    }

    private void showMovingPickOrCheckTaskView(String stopName, String frameNo, String businessType) {
        mMovingObFlowImg.setVisibility(isObFlowBusiness(businessType) ? View.VISIBLE : View.GONE);
        mMovingObImg.setVisibility(isObBusiness(businessType) ? View.VISIBLE : View.GONE);
        setMovingStopName(stopName);
        mMovingFrameNoTv.setVisibility(View.VISIBLE);
        mMovingFrameNoTv.setText(String.format(getString(R.string.string_vehicle_serial),frameNo));
    }


    private void setMovingStopName(String stopName) {
        LogUtils.i(TAG, "setMovingStopName===>stopName: " + stopName);
        if (stopName != null && stopName.contains("-")) {
            int index =  stopName.indexOf("-");
            if (index != -1) {
                mMovingStopTypeTv.setText(stopName.substring(0, index));
                mMovingStopTv.setText(stopName.substring(index + 1));
            }
        }else {
            mMovingStopTv.setText(stopName);
        }
    }

    private void sendArriveCmdToRobot(){
        if(mRosService == null){
            mRosService = new RosServiceImpl();
        }
        TaskInfoStartEvent taskInfo = GlobalRobotDataManager.getInstance().getTaskStartInfo();
        if(taskInfo == null){
            LogUtils.e(TAG, "sendArriveCmdToRobot===>taskInfo is null");
            return;
        };
        mRosService.robotAsArrived(taskInfo.getTaskId(), new AbsMessageListener<ArrivedResp>() {
            @Override
            public void onSuccess(ArrivedResp resp) {
                LogUtils.i(TAG, "sendArriveCmdToRobot===>发送成功：resp: " + resp.toString());
            }

            @Override
            public void onError(int code, String errorMsg) {
                LogUtils.e(TAG, "sendArriveCmdToRobot===>发送失败：msg: " + errorMsg);
            }
        });
    }

    @Subscribe
    public void onTaskEndEvent(TaskInfoEndEvent event){
        if(event != null){
            LogUtils.i(TAG, "onTaskEndEvent===>event: " + event.toString());
            //判断任务类型，进入不同界面
            if(isBindTask(event.getTaskType()) || isPickTask(event.getTaskType()) || isRecheckTask(event.getTaskType())){
                //进入业务到达页面
                enterActivity(PickOwhsActivity.class);
            }else{
                //进入初始化页面
                enterActivity(InitActivity.class);
            }
            finish();
        }
    }

}
