package com.jd.amr.hmi.model.data.http;

import com.google.gson.Gson;

public class OssUploadPreSignedUrlRequestBody extends CommonRequest{
    private String fileKey;
    private String bucketName;

    public OssUploadPreSignedUrlRequestBody(){}

    public OssUploadPreSignedUrlRequestBody(String fileKey, String bucketName) {
        this.fileKey = fileKey;
        this.bucketName = bucketName;
    }

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    @Override
    public String makeRequestBody() {
        return new Gson().toJson(this);
    }
}
