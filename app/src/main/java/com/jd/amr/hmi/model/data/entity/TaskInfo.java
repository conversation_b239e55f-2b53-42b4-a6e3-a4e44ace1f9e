package com.jd.amr.hmi.model.data.entity;

import com.jd.amr.hmi.manager.http.base.AndroidResponse;

/**
 * 任务信息实体类
 * 继承自AndroidResponse，用于表示任务的详细信息
 */
public class TaskInfo extends AndroidResponse {

    /**
     * 业务编号
     */
    private String businessNo;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 任务编号
     */
    private String taskNo;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 任务类型名称
     */
    private String taskTypeName;
    
    /**
     * 是否为业务任务
     */
    private boolean isBusinessTask;
    
    /**
     * 最晚出库时间
     */
    private String latestOutTime;
    
    /**
     * 任务状态
     */
    private String taskStatus;
    
    /**
     * 货架类型名称
     */
    private String shelfTypeName;
    
    /**
     * 货架编号
     */
    private String shelfNo;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 子任务ID
     */
    private String subTaskId;
    
    /**
     * 子任务状态
     */
    private String subTaskStatus;
    
    /**
     * 目标动作
     */
    private int toAction;
    
    /**
     * 是否申请更换机器人
     */
    private boolean applyChangeRobot;
    
    /**
     * 新机器人ID
     */
    private String newRobot;
    
    /**
     * 当前停靠点
     */
    private ParkPoint curPoint;
    
    /**
     * 下一个停靠点
     */
    private ParkPoint nextPoint;

    /**
     * 默认构造函数
     */
    public TaskInfo() {
    }

    /**
     * 获取业务编号
     * 
     * @return 业务编号
     */
    public String getBusinessNo() {
        return businessNo;
    }

    /**
     * 设置业务编号
     * 
     * @param businessNo 业务编号
     */
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    /**
     * 获取业务类型
     * 
     * @return 业务类型
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * 设置业务类型
     * 
     * @param businessType 业务类型
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取任务编号
     * 
     * @return 任务编号
     */
    public String getTaskNo() {
        return taskNo;
    }

    /**
     * 设置任务编号
     * 
     * @param taskNo 任务编号
     */
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    /**
     * 获取任务类型
     * 
     * @return 任务类型
     */
    public String getTaskType() {
        return taskType;
    }

    /**
     * 设置任务类型
     * 
     * @param taskType 任务类型
     */
    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    /**
     * 获取任务类型名称
     * 
     * @return 任务类型名称
     */
    public String getTaskTypeName() {
        return taskTypeName;
    }

    /**
     * 设置任务类型名称
     * 
     * @param taskTypeName 任务类型名称
     */
    public void setTaskTypeName(String taskTypeName) {
        this.taskTypeName = taskTypeName;
    }

    /**
     * 是否为业务任务
     * 
     * @return 是否为业务任务
     */
    public boolean isBusinessTask() {
        return isBusinessTask;
    }

    /**
     * 设置是否为业务任务
     * 
     * @param businessTask 是否为业务任务
     */
    public void setBusinessTask(boolean businessTask) {
        isBusinessTask = businessTask;
    }

    /**
     * 获取最晚出库时间
     * 
     * @return 最晚出库时间
     */
    public String getLatestOutTime() {
        return latestOutTime;
    }

    /**
     * 设置最晚出库时间
     * 
     * @param latestOutTime 最晚出库时间
     */
    public void setLatestOutTime(String latestOutTime) {
        this.latestOutTime = latestOutTime;
    }

    /**
     * 获取任务状态
     * 
     * @return 任务状态
     */
    public String getTaskStatus() {
        return taskStatus;
    }

    /**
     * 设置任务状态
     * 
     * @param taskStatus 任务状态
     */
    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    /**
     * 获取货架类型名称
     * 
     * @return 货架类型名称
     */
    public String getShelfTypeName() {
        return shelfTypeName;
    }

    /**
     * 设置货架类型名称
     * 
     * @param shelfTypeName 货架类型名称
     */
    public void setShelfTypeName(String shelfTypeName) {
        this.shelfTypeName = shelfTypeName;
    }

    /**
     * 获取货架编号
     * 
     * @return 货架编号
     */
    public String getShelfNo() {
        return shelfNo;
    }

    /**
     * 设置货架编号
     * 
     * @param shelfNo 货架编号
     */
    public void setShelfNo(String shelfNo) {
        this.shelfNo = shelfNo;
    }

    /**
     * 获取任务ID
     * 
     * @return 任务ID
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * 设置任务ID
     * 
     * @param taskId 任务ID
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 获取子任务ID
     * 
     * @return 子任务ID
     */
    public String getSubTaskId() {
        return subTaskId;
    }

    /**
     * 设置子任务ID
     * 
     * @param subTaskId 子任务ID
     */
    public void setSubTaskId(String subTaskId) {
        this.subTaskId = subTaskId;
    }

    /**
     * 获取子任务状态
     * 
     * @return 子任务状态
     */
    public String getSubTaskStatus() {
        return subTaskStatus;
    }

    /**
     * 设置子任务状态
     * 
     * @param subTaskStatus 子任务状态
     */
    public void setSubTaskStatus(String subTaskStatus) {
        this.subTaskStatus = subTaskStatus;
    }

    /**
     * 获取目标动作
     * 
     * @return 目标动作
     */
    public int getToAction() {
        return toAction;
    }

    /**
     * 设置目标动作
     * 
     * @param toAction 目标动作
     */
    public void setToAction(int toAction) {
        this.toAction = toAction;
    }

    /**
     * 是否申请更换机器人
     * 
     * @return 是否申请更换机器人
     */
    public boolean isApplyChangeRobot() {
        return applyChangeRobot;
    }

    /**
     * 设置是否申请更换机器人
     * 
     * @param applyChangeRobot 是否申请更换机器人
     */
    public void setApplyChangeRobot(boolean applyChangeRobot) {
        this.applyChangeRobot = applyChangeRobot;
    }

    /**
     * 获取新机器人ID
     * 
     * @return 新机器人ID
     */
    public String getNewRobot() {
        return newRobot;
    }

    /**
     * 设置新机器人ID
     * 
     * @param newRobot 新机器人ID
     */
    public void setNewRobot(String newRobot) {
        this.newRobot = newRobot;
    }

    /**
     * 获取当前停靠点
     * 
     * @return 当前停靠点
     */
    public ParkPoint getCurPoint() {
        return curPoint;
    }

    /**
     * 设置当前停靠点
     * 
     * @param curPoint 当前停靠点
     */
    public void setCurPoint(ParkPoint curPoint) {
        this.curPoint = curPoint;
    }

    /**
     * 获取下一个停靠点
     * 
     * @return 下一个停靠点
     */
    public ParkPoint getNextPoint() {
        return nextPoint;
    }

    /**
     * 设置下一个停靠点
     * 
     * @param nextPoint 下一个停靠点
     */
    public void setNextPoint(ParkPoint nextPoint) {
        this.nextPoint = nextPoint;
    }

    @Override
    public String toString() {
        return "TaskInfo{" +
                "businessNo='" + businessNo + '\'' +
                ", businessType='" + businessType + '\'' +
                ", taskNo='" + taskNo + '\'' +
                ", taskType='" + taskType + '\'' +
                ", taskTypeName='" + taskTypeName + '\'' +
                ", isBusinessTask=" + isBusinessTask +
                ", latestOutTime='" + latestOutTime + '\'' +
                ", taskStatus='" + taskStatus + '\'' +
                ", shelfTypeName='" + shelfTypeName + '\'' +
                ", shelfNo='" + shelfNo + '\'' +
                ", taskId='" + taskId + '\'' +
                ", subTaskId='" + subTaskId + '\'' +
                ", subTaskStatus='" + subTaskStatus + '\'' +
                ", toAction=" + toAction +
                ", applyChangeRobot=" + applyChangeRobot +
                ", newRobot='" + newRobot + '\'' +
                ", curPoint=" + curPoint +
                ", nextPoint=" + nextPoint +
                '}';
    }
}
