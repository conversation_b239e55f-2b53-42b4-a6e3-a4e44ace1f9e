package com.jd.amr.hmi.manager.http;

import com.jd.amr.hmi.manager.http.base.AndroidResponse;
import com.jd.amr.hmi.model.data.entity.ParkPointListResponse;
import com.jd.amr.hmi.model.data.entity.TaskInfo;
import com.jd.amr.hmi.model.data.entity.OssUploadPreSignedUrlResponse;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.BaseResponse;
import io.reactivex.rxjava3.core.Observable;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * 任务相关接口
 */
public interface ITaskApi {

    /**
     * 绑定上装
     * @param body 入参
     * @return 结果
     */
    @Headers("Content-type:application/json")
    @POST("/integrate/hmi/biz/bind-shelf")
    Observable<BaseResult<AndroidResponse>> bindShelfApi(@Body RequestBody body);

    /**
     * 上报任务完成
     * @param body 入参
     * @return 结果
     */
    @Headers("Content-type:application/json")
    @POST("/integrate/hmi/biz/complete-task")
    Observable<BaseResult<AndroidResponse>> reportTaskComplete(@Body RequestBody body);

    /**
     * 获取任务信息
     * @param body 入参
     * @return 任务信息
     */
    @Headers("Content-type:application/json")
    @POST("/integrate/hmi/biz/get-task-info")
    Observable<BaseResult<TaskInfo>> getTaskInfo(@Body RequestBody body);

    /**
     * 获取地图点位信息
     * @param body 入参
     * @return 地图点位信息
     */
    @Headers("Content-type:application/json")
    @POST("/integrate/hmi/biz/get-point-list")
    Observable<BaseResult<ParkPointListResponse>> getPointList(@Body RequestBody body);

    /**
     * 获取OSS上传预签名地址
     * @param body 入参
     * @return OSS上传预签名地址信息
     */
    @Headers("Content-type:application/json")
    @POST("/infrastructure/oss/getPreUrl")
    Observable<BaseResult<OssUploadPreSignedUrlResponse>> getOssUploadPreSignedUrl(@Body RequestBody body);
}
