package com.jd.amr.hmi.model.data.entity;

import com.jd.amr.hmi.manager.http.base.AndroidResponse;

import java.util.List;

/**
 * 停靠点列表响应实体类
 * 继承自AndroidResponse，用于表示停靠点列表的响应数据
 *
 */
public class ParkPointListResponse extends AndroidResponse {

    /**
     * 停靠点列表
     */
    private List<SimpleParkPoint> parkPointList;

    /**
     * 默认构造函数
     */
    public ParkPointListResponse() {
    }

    /**
     * 获取停靠点列表
     * 
     * @return 停靠点列表
     */
    public List<SimpleParkPoint> getParkPointList() {
        return parkPointList;
    }

    /**
     * 设置停靠点列表
     * 
     * @param parkPointList 停靠点列表
     */
    public void setParkPointList(List<SimpleParkPoint> parkPointList) {
        this.parkPointList = parkPointList;
    }

    @Override
    public String toString() {
        return "ParkPointListResponse{" +
                "parkPointList=" + parkPointList +
                '}';
    }
}
