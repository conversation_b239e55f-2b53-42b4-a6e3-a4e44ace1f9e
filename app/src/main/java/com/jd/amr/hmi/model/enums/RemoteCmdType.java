package com.jd.amr.hmi.model.enums;

public enum RemoteCmdType {

    CMD_REMOTE_CALL("EVENT_OPERATION_REMOTE_CALL", "上报远程呼叫设备"),

    CMD_OPERATION_FOLLOW("CMD_OPERATION_FOLLOW", "监控同步HMI跟进中"),

    CMD_ALARM_UPDATE("CMD_ALARM_UPDATE", "同步告警状态"),

    CMD_GET_OTA_TASK("OTA_TASK", "下发升级任务"),
    CMD_SEND_OTA_TASK_STATUS("OTA_TASK_STATUS", "上报升级任务状态"),
    CMD_SEND_OTA_VERSION("OTA_VERSION", "上报应用版本"),

    CMD_TASK_CHANGE("CMD_TASK_CHANGE", "任务变更"),
    CMD_UPLOAD_LOG("CMD_UPLOAD_LOG", "上传日志");


    private String type;

    private String name;

    RemoteCmdType(String type,  String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
