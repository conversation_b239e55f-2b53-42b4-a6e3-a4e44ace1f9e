package com.jd.amr.hmi.model.enums;

/**
 * OTA任务状态枚举
 */
public enum OtaTaskStatus {
    /**
     * 已终止
     */
    TERMINATED(-2, "已终止"),

    /**
     * 已取消
     */
    CANCELLED(-1, "已取消"),

    /**
     * 待生效
     */
    PENDING(0, "待生效"),

    /**
     * 已接收
     */
    RECEIVED(1, "已接收"),

    /**
     * 下载中
     */
    DOWNLOADING(2, "下载中"),

    /**
     * 下载完成
     */
    DOWNLOAD_COMPLETED(3, "下载完成"),

    /**
     * 安装中
     */
    INSTALLING(4, "安装中"),

    /**
     * 安装完成
     */
    INSTALL_COMPLETED(5, "安装完成"),

    /**
     * 安装失败
     */
    INSTALL_FAILED(6, "安装失败"),

    /**
     * 创建中
     */
    CREATING(7, "创建中"),

    /**
     * 创建失败
     */
    CREATE_FAILED(8, "创建失败"),

    /**
     * 已生效
     */
    EFFECTIVE(9, "已生效"),

    /**
     * 下载失败
     */
    DOWNLOAD_FAILED(10, "下载失败");

    private final int code;
    private final String description;

    OtaTaskStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static OtaTaskStatus fromCode(int code) {
        for (OtaTaskStatus status : OtaTaskStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断当前状态是否为终态
     *
     * @return 是否为终态
     */
    public boolean isTerminalState() {
        return this == TERMINATED || this == CANCELLED || 
               this == INSTALL_COMPLETED || this == INSTALL_FAILED || 
               this == CREATE_FAILED || this == EFFECTIVE || 
               this == DOWNLOAD_FAILED;
    }

    /**
     * 判断当前状态是否为失败状态
     *
     * @return 是否为失败状态
     */
    public boolean isFailedState() {
        return this == INSTALL_FAILED || this == CREATE_FAILED || 
               this == DOWNLOAD_FAILED;
    }

    /**
     * 判断当前状态是否为进行中状态
     *
     * @return 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == DOWNLOADING || this == INSTALLING || 
               this == CREATING;
    }

    @Override
    public String toString() {
        return description;
    }
}
