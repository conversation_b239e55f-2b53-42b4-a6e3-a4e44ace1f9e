package com.jd.amr.hmi.mqtt;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;


import androidx.core.app.NotificationCompat;

import com.google.gson.Gson;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.model.data.entity.OtaTaskStatusInfo;
import com.jd.amr.hmi.model.data.entity.OtaTaskVersion;
import com.jd.amr.hmi.model.data.mqtt.MqttRequestData;
import com.jd.amr.hmi.model.data.mqtt.MqttRequestHeader;
import com.jd.amr.hmi.model.data.mqtt.MqttResponseData;
import com.jd.amr.hmi.model.data.mqtt.MqttResponseHeader;
import com.jd.amr.hmi.model.data.mqtt.MqttWillData;
import com.jd.amr.hmi.model.enums.MqttMessageStateEnum;
import com.jd.amr.hmi.model.enums.OtaTaskStatus;
import com.jd.amr.hmi.model.enums.RemoteCmdType;
import com.jd.amr.hmi.mqtt.handler.ICmdHandler;
import com.jd.amr.hmi.util.MqttTopicUtils;
import com.jd.amr.hmi.util.NetworkUtils;
import com.jd.amr.hmi.util.VersionUtil;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.MqttUtils;

import org.eclipse.paho.android.service.MqttAndroidClient;
import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * <p>
 * mqtt service for remote command
 * </p>
 *
 * <AUTHOR>
 * @date： 2022/07/04
 */
public class JdMqttService extends Service {

    public static final String TAG = "JdMqttService";

    private String clientId;
    private MqttAndroidClient mqttClient;
    private MqttConnectOptions mqttConnectOptions;
    private Gson gson;
    private MqttRequestData<MqttWillData> mqttWillDataRequest;

    private ScheduledExecutorService scheduledExecutorService;
    private static final int MQTT_TIMEOUT = 15;
    private static final int MQTT_INTERVAL = 10;
    private static final String MQTT_URL = "jdxmqtt.jdl.com";

    public static final String ACTION_START_INIT = "ACTION_START_INIT";

    public static final String ACTION_REMOTE_CALL = "ACTION_REMOTE_CALL";

    public static final String ACTION_OTA_TASK_STATUS = "ACTION_OTA_TASK_STATUS";

    public static final String ACTION_OTA_APP_VERSION = "ACTION_OTA_APP_VERSION";


    @Override
    public void onCreate() {
        super.onCreate();
        mqttWillDataRequest = new MqttRequestData<>();
        gson = new Gson();
        scheduledExecutorService = new ScheduledThreadPoolExecutor(1);
        initNotification();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            LogUtils.i(TAG, "onStartCommand===>action: " + action);
            switch (action) {
                case ACTION_START_INIT:
                    initAndStart();
                    break;
                case ACTION_REMOTE_CALL:
                    publishCallEvent();
                    break;
                    case ACTION_OTA_TASK_STATUS:
                        Bundle bundle = intent.getExtras();
                        if(bundle != null){
                            OtaTaskStatusInfo taskStatusInfo = (OtaTaskStatusInfo) bundle.getSerializable(Constants.KEY_OTA_STATUS);
                            publishOtaStatus(taskStatusInfo);
                        }
                        break;
                case ACTION_OTA_APP_VERSION:
                    Bundle bundle1 = intent.getExtras();
                    if(bundle1 != null){
                         OtaTaskVersion taskVersion= (OtaTaskVersion) bundle1.getSerializable(Constants.KEY_OTA_VERSION);
                         publishAppVersion(taskVersion);
                     }
                    break;
                default:
                    break;
            }
        }
        //设置服务死掉后不自动拉活
        return START_NOT_STICKY;
    }

    @SuppressLint("ForegroundServiceType")
    private void initNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel("MQTT_CHANNEL_ID", "MQTT_CHANNEL_NAME", NotificationManager.IMPORTANCE_LOW);
            NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (manager == null) {
                return;
            }
            manager.createNotificationChannel(channel);
            Notification notification = new NotificationCompat.Builder(this, "MQTT_CHANNEL_ID")
                    .setAutoCancel(true)
                    .setCategory(Notification.CATEGORY_SERVICE)
                    .setOngoing(true)
                    .setPriority(NotificationManager.IMPORTANCE_LOW)
                    .build();

            startForeground(102, notification);
        }
    }

    private void initClient() {
        String vehicleName = GlobalRobotDataManager.getInstance().getRobotSn();
        String apiKey = GlobalRobotDataManager.getInstance().getApiKey();
        if (vehicleName != null && apiKey != null) {
                if (mqttClient == null) {
                    LogUtils.i(TAG, "vehicleName: " + vehicleName + ", apiKey: " + apiKey);
                    clientId = "integrate_and_" + vehicleName;
                    mqttClient = new MqttAndroidClient(this, BuildConfig.MQTT_URL, clientId);
                    mqttConnectOptions = new MqttConnectOptions();
                    mqttConnectOptions.setCleanSession(false);
                    mqttConnectOptions.setUserName(GlobalRobotDataManager.getInstance().getRobotSn());
                    mqttConnectOptions.setPassword(GlobalRobotDataManager.getInstance().getApiKey().toCharArray());
                    mqttConnectOptions.setConnectionTimeout(MQTT_TIMEOUT);
                    mqttConnectOptions.setKeepAliveInterval(MQTT_INTERVAL);
                    String topic = MqttTopicUtils.getWillTopic(vehicleName);
                    String json = getWillData(0);
//                    //设置遗嘱
                    mqttConnectOptions.setWill(topic, json.getBytes(), 2, false);
                    mqttClient.setCallback(new PushCallback());
                }
        } else {
            LogUtils.i(TAG, "vehicleName is null");
        }
    }

    /**
     * <p>
     * get will data
     * </p>
     */
    private String getWillData(int online) {
        String vehicleName = GlobalRobotDataManager.getInstance().getRobotSn();
        long requestId = MqttUtils.getRequestId();
        long requestTime = MqttUtils.getCurrentTime();
        MqttRequestHeader requestHeader = new MqttRequestHeader(null, requestId, vehicleName, requestTime, null, false);
        MqttWillData willData = new MqttWillData(online);
        mqttWillDataRequest.setHeader(requestHeader);
        mqttWillDataRequest.setData(willData);
        Gson willGson = new Gson();
        return willGson.toJson(mqttWillDataRequest);
    }

    private void initAndStart() {
        if(scheduledExecutorService == null){
            scheduledExecutorService = new ScheduledThreadPoolExecutor(1);
        }
        scheduledExecutorService.scheduleAtFixedRate(this::scheduleExecution, 0, 3, TimeUnit.SECONDS);
        scheduledExecutorService.execute(() -> {
            connect();
        });
    }

    private void connect() {
        if (mqttClient == null) {
            initClient();
        }
        doConnect();
    }

    private void doConnect() {
        if (mqttClient != null && !mqttClient.isConnected()) {
            try {
                LogUtils.i(TAG, "mqtt 开始连接, 当前线程：" + Thread.currentThread().toString());
                mqttClient.connect(mqttConnectOptions, null, new IMqttActionListener() {
                    @Override
                    public void onSuccess(IMqttToken asyncActionToken) {
                        try {
                            if(mqttClient.isConnected()) {
                                String vehicleName = GlobalRobotDataManager.getInstance().getRobotSn();
                                String[] topics = {MqttTopicUtils.getExceptionTopic(vehicleName), MqttTopicUtils.getFollowUserTopic(vehicleName),
                                MqttTopicUtils.getReportOtaTaskTopic(vehicleName), MqttTopicUtils.getSendOtaTaskTopic(vehicleName)};
                                int[] qos = {2, 2, 2, 2};
                                LogUtils.i(TAG, "mqtt 连接成功, 当前线程: " + Thread.currentThread().toString());
                                GlobalRobotDataManager.getInstance().setMqttConnected(true);
                                //连接成功后发送遗嘱消息
                                String willTopic = MqttTopicUtils.getWillTopic(GlobalRobotDataManager.getInstance().getRobotSn());
                                String json = getWillData(1);
                                mqttPublish(willTopic, json);
                                mqttClient.subscribe(topics, qos);
                                checkOtaTask();
                            }else{
                                LogUtils.i(TAG, "mqtt 连接失败，重新连接");
                                reConnect();
                            }
                        } catch (MqttException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                        LogUtils.i(TAG, "mqtt 连接失败：" + exception.toString());
                        reConnect();
                    }
                });
            } catch (MqttException e) {
                e.printStackTrace();
            }
        }
    }

    private void reConnect() {
        GlobalRobotDataManager.getInstance().setMqttConnected(false);
        if(scheduledExecutorService != null){
            scheduledExecutorService.execute(() -> {
                try {
                    Thread.sleep(3000);
                    connect();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private void disConnect(){
        if(mqttClient != null && mqttClient.isConnected() && scheduledExecutorService != null){
            scheduledExecutorService.execute(() -> {
                try {
                    LogUtils.i(TAG, "mqtt 开始断开连接, 当前线程：" + Thread.currentThread().toString());
                    mqttClient.disconnect(null, new IMqttActionListener() {
                        @Override
                        public void onSuccess(IMqttToken asyncActionToken) {
                            LogUtils.i(TAG, "mqtt 断开连接成功, 当前线程：" + Thread.currentThread().toString());
                            mqttClient.unregisterResources();
                            mqttClient.close();
                            mqttClient = null;
                        }

                        @Override
                        public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                            LogUtils.e(TAG, "mqtt 断开连接失败：" + exception.toString());
                        }
                    });
                } catch (MqttException e) {
                    LogUtils.e(TAG, "mqtt 断开连接异常：" + e.toString());
                }
            });
        }
    }


    private void scheduleExecution() {
        pingServerTask();
    }


    private void pingServerTask() {
        if (!BuildConfig.DEBUGABLE) {
            boolean isNetWork = NetworkUtils.ping(MQTT_URL, "5");
        }
    }

    public void publishReplay(MqttResponseData data) {
        if (data != null) {
            if (!mqttClient.isConnected()) {
                return;
            }
            Gson dataGson = new Gson();
            String json = dataGson.toJson(data);
            String topic = MqttTopicUtils.getMonitorReplyTopic(GlobalRobotDataManager.getInstance().getRobotSn());
            LogUtils.i(TAG, "mqtt publish message===>topic: " + topic + ", data: " + json);
            mqttPublish(topic, json);
        }
    }


    public void publishCallEvent() {
        if (mqttClient == null || !mqttClient.isConnected()) {
            return;
        }
        String vehicleName = GlobalRobotDataManager.getInstance().getRobotSn();
        MqttRequestData mqttRequestData = new MqttRequestData<>();
        mqttRequestData.setHeader(getMqttRequestHeader(RemoteCmdType.CMD_REMOTE_CALL.getType()));
        mqttRequestData.setData(new Object());
        Gson dataGson = new Gson();
        String json = dataGson.toJson(mqttRequestData);
        String topic = MqttTopicUtils.getCallDeviceTopic(vehicleName);
        LogUtils.i(TAG, "mqtt publish event message===>topic: " + topic + ", data: " + json);
        mqttPublish(topic, json);
    }

    private MqttRequestHeader getMqttRequestHeader(String remoteCmdType) {
        MqttRequestHeader mqttRequestHeader = new MqttRequestHeader();
        mqttRequestHeader.setRequestId(MqttUtils.getRequestId());
        mqttRequestHeader.setMessageType(remoteCmdType);
        mqttRequestHeader.setSendName(GlobalRobotDataManager.getInstance().getRobotSn());
        mqttRequestHeader.setRequestTime(MqttUtils.getCurrentTime());
        mqttRequestHeader.setNeedResponse(true);
        return mqttRequestHeader;
    }

    public void publishOtaStatus(OtaTaskStatusInfo otaTaskStatus){
        if (mqttClient == null || !mqttClient.isConnected()) {
            return;
        }
        if(otaTaskStatus == null){
            return;
        }
        MqttRequestData mqttRequestData = new MqttRequestData<>();
        mqttRequestData.setHeader(getMqttRequestHeader(RemoteCmdType.CMD_SEND_OTA_TASK_STATUS.getType()));
        mqttRequestData.setData(otaTaskStatus);
        publishOtaTask(mqttRequestData);
    }

    public void publishAppVersion(OtaTaskVersion otaTaskVersion){
        if (mqttClient == null || !mqttClient.isConnected()) {
            return;
        }
        if(otaTaskVersion == null){
    		return;
    	}
        MqttRequestData mqttRequestData = new MqttRequestData<>();
        mqttRequestData.setHeader(getMqttRequestHeader(RemoteCmdType.CMD_SEND_OTA_VERSION.getType()));
        mqttRequestData.setData(otaTaskVersion);
        publishOtaTask(mqttRequestData);
    }

    private void publishOtaTask(MqttRequestData data){
        Gson dataGson = new Gson();
        String json = dataGson.toJson(data);
        String topic = MqttTopicUtils.getReportOtaTaskTopic(GlobalRobotDataManager.getInstance().getRobotSn());
        LogUtils.i(TAG, "mqtt publish event message===>topic: " + topic + ", data: " + json);
        mqttPublish(topic, json);
    }


    private void mqttPublish(String topic, String json) {
        try {
            mqttClient.publish(topic, json.getBytes(), 2, false, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    LogUtils.i(TAG, "mqtt publish success: ");
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    if (exception != null) {
                        LogUtils.i(TAG, "mqtt publish failed: " + exception.toString());
                    } else {
                        LogUtils.i(TAG, "mqtt publish failed: ");
                    }

                }
            });
        } catch (MqttException e) {
            LogUtils.i(TAG, "mqtt publish exception: " + e.getMessage());
            e.printStackTrace();
        }
    }


    @Override
    public IBinder onBind(Intent intent) {
        // TODO: Return the communication channel to the service.
        throw new UnsupportedOperationException("Not yet implemented");
    }

    public class PushCallback implements MqttCallback {

        @Override
        public void connectionLost(Throwable cause) {
            if(cause != null){
                LogUtils.i(TAG, "连接断开: " + cause.toString() + ", 开始重连 ！！！");
            }else{
                LogUtils.i(TAG, "连接断开, 开始重连 ！！！");
            }
            reConnect();
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) {
            try {
                LogUtils.i(TAG, "receive topic: " + topic + ", msg: " + message.toString());
                MqttRequestHeader header = getHeaderFromMsg(message);
                if (header != null) {
                    handleRemoteCmd(message.toString(), header);
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "messageArrived===>error: " + e.getMessage());
            }
        }

        @Override
        public void deliveryComplete(IMqttDeliveryToken token) {
            LogUtils.i(TAG, "mqtt deliveryComplete: " + token.toString());
        }
    }

    private void checkOtaTask(){
        OtaTaskStatusInfo taskStatusInfo = GlobalRobotDataManager.getInstance().getOtaTaskStatusInfo();
        if(taskStatusInfo != null){
            int appVersion = VersionUtil.getInstance().getVersionCode();
            LogUtils.i(TAG, "checkOtaTask===>taskNumber: " + taskStatusInfo.getIssueTaskNumber() + " taskVersion: " + taskStatusInfo.getVersion() + ", curAppVersion: " + appVersion);
            try{
                int taskVersionCode = Integer.parseInt(taskStatusInfo.getVersion());
                if(appVersion == taskVersionCode) {
                    // 升级成功，通知云端
                    taskStatusInfo.setStatus(OtaTaskStatus.INSTALL_COMPLETED.getCode());
                    taskStatusInfo.setRemark(OtaTaskStatus.INSTALL_COMPLETED.getDescription());
                    taskStatusInfo.setTimestamp(System.currentTimeMillis());
                    publishOtaStatus(taskStatusInfo);
                    // 上报app版本
                    OtaTaskVersion otaTaskVersion = new OtaTaskVersion();
                    otaTaskVersion.setVersion(String.valueOf(appVersion));
                    otaTaskVersion.setTimestamp(System.currentTimeMillis());
                    otaTaskVersion.setProductKey(taskStatusInfo.getProductKey());
                    otaTaskVersion.setDeviceName(taskStatusInfo.getDeviceName());
                    otaTaskVersion.setAppName(taskStatusInfo.getAppName());
                    publishAppVersion(otaTaskVersion);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    private MqttRequestHeader getHeaderFromMsg(MqttMessage mqttMessage) {
        try {
            MqttRequestData requestData = gson.fromJson(mqttMessage.toString(), MqttRequestData.class);
            MqttRequestHeader header = requestData.getHeader();
            return header;
        } catch (Exception e) {
            LogUtils.e(TAG, "getHeaderFromMsg error: " + e.toString());
        }
        return null;
    }

    private void handleRemoteCmd(String msg, MqttRequestHeader header) {
        try {
            ICmdHandler cmdHandler = CmdHandlerFactory.getInstance().getHandler(header.getMessageType());
            if (cmdHandler != null) {
                cmdHandler.handleMsg(this, msg);
            } else {
                LogUtils.e(TAG, "handleRemoteCmd 没有相关的指令");
            }
            publishReplay(getMqttResponseData(header, MqttMessageStateEnum.SUCCESS.getState(), null));
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            LogUtils.e(TAG, "handleRemoteCmd error: " + errorMsg);
            publishReplay(getMqttResponseData(header, MqttMessageStateEnum.FAIL.getState(), errorMsg));
        }

    }

    private MqttResponseData getMqttResponseData(MqttRequestHeader requestHeader, String state, String failInfo) {
        MqttResponseHeader header = new MqttResponseHeader();
        header.setMessageType(requestHeader.getMessageType());
        header.setRequestId(requestHeader.getRequestId());
        header.setSendName(requestHeader.getSendName());
        header.setRequestTime(requestHeader.getRequestTime());
        long responseTime = MqttUtils.getCurrentTime();
        header.setResponseTime(responseTime);
        header.setMessageState(state);
        header.setFailInfo(failInfo);
        MqttResponseData mqttResponseData = new MqttResponseData();
        mqttResponseData.setHeader(header);
        return mqttResponseData;
    }


    @Override
    public void onDestroy() {
        disConnect();
        LogUtils.i(TAG, "JdMqttService onDestroy");
        super.onDestroy();
    }

}