package com.jd.amr.hmi.ui.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.appcompat.widget.AppCompatEditText;

public class CodeEditText extends AppCompatEditText {

    public CodeEditText(Context context) {
        super(context);
    }

    public CodeEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CodeEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return false;
    }
}
