package com.jd.amr.hmi.model.data.entity;

/**
 * 版本信息详情实体
 */
public class VersionInfoDetail {

    /**
     * 应用系统名称
     * 例如：amr_hmi（自动导引车人机交互系统）
     */
    private String appName;

    /**
     * 应用显示别名
     * 用于界面展示的友好名称，如"AGV控制中枢系统"
     */
    private String appAlias;

    /**
     * 应用类型分类
     * 可选值：system/application/module（系统级/应用级/模块级）
     */
    private String appType;

    /**
     * 语义化版本号（SemVer）
     * 格式：主版本号.次版本号.修订号，如2.1.3
     */
    private String version;

    /**
     * 版本发布工单编号
     * 关联的JIRA工单或变更管理系统编号
     */
    private String issueTaskNumber;

    /**
     * 安装包优先级
     * 紧急程度分级：critical/high/medium/low
     */
    private String packagePriority;

    /**
     * 执行方式配置
     * 安装执行策略：immediate（立即执行）/schedule（定时执行）
     */
    private String executeType;

    /**
     * 下载方式策略
     * 传输协议：http/https/ftp 或 p2p（点对点传输）
     */
    private String downloadType;

    /**
     * 升级类型标识
     * 升级模式：full（全量更新）/delta（增量更新）
     */
    private String upgradeType;

    /**
     * 后升级操作指令
     * 安装完成后执行的操作：restart（重启服务）/reboot（重启设备）
     */
    private String afterUpgrade;

    /**
     * 关联应用重启列表
     * 需要联动重启的依赖服务列表，逗号分隔（如：scheduler_service,log_service）
     */
    private String afterUpgradeApps;

    /**
     * 版本变更说明
     * Markdown格式的更新日志，包含功能变更和修复列表
     */
    private String description;

    /**
     * 安装包文件名
     * 完整的文件名称（含扩展名），如amr_hmi_v2.1.3.apk
     */
    private String fileName;

    /**
     * 下载地址URL
     * 包含鉴权token的完整下载路径，如https://cdn.jd.com/packages/amr_hmi_v2.1.3.apk?token=xxx
     */
    private String url;

    /**
     * 安装包格式类型
     * 文件格式：apk/deb/rpm/tar.gz/docker-image
     */
    private String packageType;

    /**
     * 文件MD5校验码
     * 用于完整性验证的128位哈希值（十六进制字符串）
     */
    private String md5;

    /**
     * 包体大小（字节数）
     * 文件的实际字节长度，用于下载进度计算
     */
    private String contentLength;

    /**
     * 扩展元数据字段
     * JSON格式的附加信息，如：
     * {
     *   "digitalSignature": "base64string",
     *   "dependencyCheck": true,
     *   "compatibility": ["AGV3000", "AGV2500"]
     * }
     */
    private String extData;


    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppAlias() {
        return appAlias;
    }

    public void setAppAlias(String appAlias) {
        this.appAlias = appAlias;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getIssueTaskNumber() {
        return issueTaskNumber;
    }

    public void setIssueTaskNumber(String issueTaskNumber) {
        this.issueTaskNumber = issueTaskNumber;
    }

    public String getPackagePriority() {
        return packagePriority;
    }

    public void setPackagePriority(String packagePriority) {
        this.packagePriority = packagePriority;
    }

    public String getExecuteType() {
        return executeType;
    }

    public void setExecuteType(String executeType) {
        this.executeType = executeType;
    }

    public String getDownloadType() {
        return downloadType;
    }

    public void setDownloadType(String downloadType) {
        this.downloadType = downloadType;
    }

    public String getUpgradeType() {
        return upgradeType;
    }

    public void setUpgradeType(String upgradeType) {
        this.upgradeType = upgradeType;
    }

    public String getAfterUpgrade() {
        return afterUpgrade;
    }

    public void setAfterUpgrade(String afterUpgrade) {
        this.afterUpgrade = afterUpgrade;
    }

    public String getAfterUpgradeApps() {
        return afterUpgradeApps;
    }

    public void setAfterUpgradeApps(String afterUpgradeApps) {
        this.afterUpgradeApps = afterUpgradeApps;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getContentLength() {
        return contentLength;
    }

    public void setContentLength(String contentLength) {
        this.contentLength = contentLength;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    @Override
    public String toString() {
        return "VersionInfoDetail{" +
                "appName='" + appName + '\'' +
                ", appAlias='" + appAlias + '\'' +
                ", appType='" + appType + '\'' +
                ", version='" + version + '\'' +
                ", issueTaskNumber='" + issueTaskNumber + '\'' +
                ", packagePriority='" + packagePriority + '\'' +
                ", executeType='" + executeType + '\'' +
                ", downloadType='" + downloadType + '\'' +
                ", upgradeType='" + upgradeType + '\'' +
                ", afterUpgrade='" + afterUpgrade + '\'' +
                ", afterUpgradeApps='" + afterUpgradeApps + '\'' +
                ", description='" + description + '\'' +
                ", fileName='" + fileName + '\'' +
                ", url='" + url + '\'' +
                ", packageType='" + packageType + '\'' +
                ", md5='" + md5 + '\'' +
                ", contentLength='" + contentLength + '\'' +
                ", extData='" + extData + '\'' +
                '}';
    }
}
