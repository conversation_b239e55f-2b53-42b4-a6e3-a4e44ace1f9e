package com.jd.amr.hmi.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;

import com.google.gson.Gson;
import com.iscas.rcljava.entity.topic.VehicleInfo;
import com.jd.amr.hmi.Amr;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.R;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.impl.model.TaskDataModel;
import com.jd.amr.hmi.manager.http.impl.model.TokenDataModel;
import com.jd.amr.hmi.model.data.entity.OssUploadPreSignedUrlResponse;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.model.data.event.VehicleInfoEvent;
import com.jd.amr.hmi.model.data.http.OssUploadPreSignedUrlRequestBody;
import com.jd.amr.hmi.model.data.http.ShelfRequestBody;
import com.jd.amr.hmi.mqtt.JdMqttService;
import com.jd.amr.hmi.util.FileUtil;
import com.jd.amr.hmi.util.LongPressTouchListener;
import com.jd.amr.hmi.util.ServiceUtils;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.TokenData;
import com.jd.ugv.pad.common.base.TokenInfoData;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.StringUtils;

import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.util.List;


public class InitActivity extends BaseActivity {

    private static final String TAG = "InitActivity";

    private ConstraintLayout mInitWholeCl;

    private TokenDataModel mTokenDataModel;

    private LongPressTouchListener mLongPressTouchListener;

    private static final String[] PERMISSIONS_STORAGE = {
            "android.permission.READ_EXTERNAL_STORAGE",
            "android.permission.WRITE_EXTERNAL_STORAGE",
            "android.permission.READ_PHONE_STATE",
            "android.permission.ACCESS_FINE_LOCATION"};


    @Override
    protected Activity getActivity() {
        return this;
    }

    @Override
    protected int layoutId() {
        return R.layout.activity_init;
    }

    @Override
    protected void initView() {
        mInitWholeCl = findViewById(R.id.init_whole_cl);
    }

    @Override
    protected void initListener() {
        //长按10秒跳转到登录页
        if(mLongPressTouchListener == null){
             mLongPressTouchListener = new LongPressTouchListener(new LongPressTouchListener.LongPressListener() {
                 @Override
                 public void onLongPressSuccess() {
                     mLongPressTouchListener.release();
                     enterActivity(LoginActivity.class);
                 }

                 @Override
                 public void onLongPressProgress(int progress) {

                 }

                 @Override
                 public void onLongPressCancelled() {

                 }
             });
        }
        mInitWholeCl.setOnTouchListener(mLongPressTouchListener);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    protected void initData() {
        verifyStoragePermissions(this);
        if(BuildConfig.DEBUG){
            String debugConfig = FileUtil.readFromFile(Constants.ROBOT_DEBUG_CONFIG);
            LogUtils.i(TAG, "initData===>debugConfig: " + debugConfig);
            if(!TextUtils.isEmpty(debugConfig)){
                VehicleInfo vehicleInfo = new Gson().fromJson(debugConfig, VehicleInfo.class);
                GlobalRobotDataManager.getInstance().setVehicleInfo(vehicleInfo);
                startMqttService();
            }
        }

        if(!hasToken()){
           getTokenInfo();
        }
    }


    @Subscribe
    public void onVehicleInfoEvent(VehicleInfoEvent event) {
        if(event != null) {
            LogUtils.i(TAG, "onVehicleInfoEvent===>event: " + event.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    String robotName = StringUtils.extractIntegers(event.getVehicleInfo().robot_vin);
                    mRobotNameTv.setText(robotName);
                    //注册mqtt服务
                    if (ServiceUtils.isServiceRunning(Amr.getInstance(), JdMqttService.class.getName())) {
                        return;
                    }
                    startMqttService();
                    //获取token
                    getTokenInfo();
                }
            });
        }
    }

    @Subscribe
    public void onTaskStartEvent(TaskInfoStartEvent event){
        if(event != null){
            //进入行走页面
            enterActivity(MovingActivity.class);
        }
    }

    public static void verifyStoragePermissions(Activity activity) {
        try {
            int permission = ActivityCompat.checkSelfPermission(activity,
                    "android.permission.WRITE_EXTERNAL_STORAGE");
            if (permission != PackageManager.PERMISSION_GRANTED) {
                // request write permission
                ActivityCompat.requestPermissions(activity, PERMISSIONS_STORAGE, 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
        }
    }


    private void startMqttService(){
        if(mJdMqttService == null) {
            mJdMqttService = new Intent(this, JdMqttService.class);
        }
        mJdMqttService.setAction(JdMqttService.ACTION_START_INIT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(mJdMqttService);
        } else {
            startService(mJdMqttService);
        }
    }

    private void getTokenInfo(){
        if(mTokenDataModel == null){
            mTokenDataModel = new TokenDataModel();
        }
        String robotName = GlobalRobotDataManager.getInstance().getRobotSn();
        String apiKey = GlobalRobotDataManager.getInstance().getApiKey();
        LogUtils.i(TAG, "getTokenInfo===>robotName: " + robotName + ", apiKey: " + apiKey);
        if(robotName != null && apiKey != null){
            mTokenDataModel.getTokenInfo(robotName, apiKey, new BaseCallBack() {
                @Override
                public void success(BaseResult baseResult) {
                    TokenInfoData tokenInfoData = (TokenInfoData) baseResult.getData();
                    if (tokenInfoData != null) {
                        TokenData tokenData = new TokenData(tokenInfoData.getAccessToken(), robotName, tokenInfoData.getExpiresIn(), BuildConfig.LOP_DN);
                        tokenData.setRobotSn(robotName);
                        tokenData.setApkKey(apiKey);
                        tokenData.setUrl(BuildConfig.PROXY_URL + Constants.TOKEN);
                        GlobalRobotDataManager.getInstance().setTokenData(tokenData);
                    }
                }

                @Override
                public boolean failOrError(BaseResult baseResult, String e) {
                    LogUtils.e(TAG, "getTokenInfo failOrError: " + e);
                    return false;
                }
            });
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtils.i(TAG, "=========InitActivity onDestroy=========");
        if (mJdMqttService != null) {
            stopService(mJdMqttService);
        }
        if(mRobotRemoteService != null){
            stopService(mRobotRemoteService);
        }
    }
}
