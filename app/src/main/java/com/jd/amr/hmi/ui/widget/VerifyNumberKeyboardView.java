package com.jd.amr.hmi.ui.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;


import com.jd.amr.hmi.R;
import com.jd.amr.hmi.util.DisplayTypedValueUtil;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;



public class VerifyNumberKeyboardView extends View {
    private final static String TAG = VerifyNumberKeyboardView.class.getSimpleName();
    /**
     * 列
     */
    private static final int TOTAL_COL = 3;
    /**
     * 行
     */
    private static final int TOTAL_ROW = 4;

    private Paint bgPaint, mNumPaint, mSrcPaint;

    private Typeface mTypeface;
    private int mViewWidth; // 键盘宽度
    private int mViewHeight; // 键盘高度
    private float mCellWidth, mCellHeight; // 单元格宽度、高度
    private float numberPadding, numberSize;
    private int numNormalResId, numPressedResId, numDeleteNormalResId, numDeletePressedResId, numResetNormalResId, numResetPressedResId;
    private Bitmap numNormalBg, numPressedBg, numDeleteNormalBg, numDeletePressedBg, numResetNormalBg, numResetPressedBg;
    private final float defaultNumPadding = 0;
    private final float defaultNumSize = 48;

    private final String deleteStr = "删除";
    private final String resetStr = "重置";
    private CallBack mCallBack;// 回调

    private Row[] rows = new Row[TOTAL_ROW];
    private List<String> numKeys = Arrays.asList("1", "2", "3", "4", "5", "6", "7", "8", "9", "0");


    public VerifyNumberKeyboardView(Context context) {
        super(context);
    }

    public VerifyNumberKeyboardView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public VerifyNumberKeyboardView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.VerifyNumberKeyboardView);
        numberPadding = typedArray.getDimensionPixelSize(R.styleable.VerifyNumberKeyboardView_verifyNumberPadding, DisplayTypedValueUtil.dip2px(context, defaultNumPadding));
        numberSize = typedArray.getDimensionPixelSize(R.styleable.VerifyNumberKeyboardView_verifyNumberSize, DisplayTypedValueUtil.sp2px(context, defaultNumSize));
        //常规数据图片资源
        numNormalResId = typedArray.getResourceId(R.styleable.VerifyNumberKeyboardView_verifyNumberNormalSrc, R.mipmap.bg_number_keyboard_normal);
        numPressedResId = typedArray.getResourceId(R.styleable.VerifyNumberKeyboardView_verifyNumberPressedSrc, R.mipmap.bg_number_keyboard_pressed);
        //删除图片资源
        numDeleteNormalResId = typedArray.getResourceId(R.styleable.VerifyNumberKeyboardView_verifyNumberDeleteNormalSrc, R.mipmap.ic_number_keyboard_delete_normal);
        numDeletePressedResId = typedArray.getResourceId(R.styleable.VerifyNumberKeyboardView_verifyNumberDeletePressedSrc, R.mipmap.ic_number_keyboard_delete_pressed);
        //重置图片资源
        numResetNormalResId = typedArray.getResourceId(R.styleable.VerifyNumberKeyboardView_verifyNumberResetNormalSrc, R.mipmap.ic_number_keyboard_reset_normal);
        numResetPressedResId = typedArray.getResourceId(R.styleable.VerifyNumberKeyboardView_verifyNumberResetPressedSrc, R.mipmap.ic_number_keyboard_reset_pressed);

        typedArray.recycle();

        init();

    }

    /**
     * 初始化画笔
     *
     * @param
     */
    private void init() {

        initBitmap();

        initPaint();

        initData();
    }

    private void initBitmap() {
        numNormalBg = BitmapFactory.decodeResource(getResources(), numNormalResId);
        numPressedBg = BitmapFactory.decodeResource(getResources(), numPressedResId);

        numDeleteNormalBg = BitmapFactory.decodeResource(getResources(), numDeleteNormalResId);
        numDeletePressedBg = BitmapFactory.decodeResource(getResources(), numDeletePressedResId);

        numResetNormalBg = BitmapFactory.decodeResource(getResources(), numResetNormalResId);
        numResetPressedBg = BitmapFactory.decodeResource(getResources(), numResetPressedResId);
    }

    private void initPaint() {
//        String fontPath = "fonts/JDZhengHeiTi.TTF";
//        mTypeface = Typeface.createFromAsset(getContext().getAssets(), fontPath);

        bgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        bgPaint.setStyle(Paint.Style.FILL_AND_STROKE);


        mNumPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
//        mNumPaint.setTypeface(mTypeface);
        mNumPaint.setTextSize(numberSize);

        mSrcPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mSrcPaint.setStyle(Paint.Style.FILL_AND_STROKE);


    }

    private void initData() {
        fillKeyboardData();
    }

    /**
     * 填充数字
     */
    private void fillKeyboardData() {
        int position = 0;
        for (int i = 0; i < TOTAL_ROW; i++) {
            rows[i] = new Row(i);
            for (int j = 0; j < TOTAL_COL; j++) {
                if (i == 3 && j == 0) {
                    rows[i].cells[j] = new Cell(resetStr, State.DEFAULT_NUM, i, j);
                } else if (i == 3 && j == 2) {
                    rows[i].cells[j] = new Cell(deleteStr, State.DEFAULT_NUM, i, j);
                } else {
                    rows[i].cells[j] = new Cell(numKeys.get(position), State.DEFAULT_NUM, i, j);
                    position++;
                }
            }
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mViewWidth = w;
        mViewHeight = h;
        mCellWidth = (mViewWidth - (TOTAL_COL - 1) * numberPadding) / TOTAL_COL;
        mCellHeight = (mViewHeight - (TOTAL_ROW - 1) * numberPadding) / TOTAL_ROW;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        for (int i = 0; i < TOTAL_ROW; i++) {
            if (rows[i] != null) {
                rows[i].drawCells(canvas);
            }
        }
    }


    private Cell mClickCell = null;
    private float mDownX;
    private float mDownY;

    /*
     *
     * 触摸事件为了确定点击位置的数字
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = event.getX();
                mDownY = event.getY();
                int col = -1;
                int row = -1;
                for (int i = 0; i < TOTAL_COL; i++) {
                    if (i * (numberPadding + mCellWidth) <= mDownX &&
                            mDownX <= (i * (numberPadding + mCellWidth) + mCellWidth)) {
                        col = i;
                    }
                }
                for (int j = 0; j < TOTAL_ROW; j++) {
                    if (j * (numberPadding + mCellHeight) <= mDownY &&
                            mDownY <= (j * (numberPadding + mCellHeight) + mCellHeight)) {
                        row = j;
                    }
                }
                Log.d(TAG, "onTouchEvent: col-" + col + ", row-" + row);
                measureClickCell(col, row);
                break;
            case MotionEvent.ACTION_UP:
                if (mClickCell != null) {
                    // 在抬起后把状态置为默认
                    rows[mClickCell.i].cells[mClickCell.j].state = State.DEFAULT_NUM;
                    mClickCell = null;
                    invalidate();
                }
                break;
            default:
                break;
        }
        return true;
    }

    /**
     * 测量点击单元格
     *
     * @param col 列
     * @param row 行
     */
    private void measureClickCell(int col, int row) {
        if (col >= TOTAL_COL || row >= TOTAL_ROW) {
            return;
        }
        if (col == -1 || row == -1) {
            return;
        }
        if (rows[row] != null) {
            mClickCell = new Cell(rows[row].cells[col].num, rows[row].cells[col].state, rows[row].cells[col].i,
                    rows[row].cells[col].j);
            rows[row].cells[col].state = State.CLICK_NUM;
            if (deleteStr.equals(rows[row].cells[col].num)) {
                if (mCallBack != null) {
                    mCallBack.deleteNum();
                }
            } else if (resetStr.equals(rows[row].cells[col].num)) {
                if (mCallBack != null) {
                    mCallBack.resetNum();
                }
            } else {
                if (mCallBack != null) {
                    mCallBack.clickNum(rows[row].cells[col].num);
                }
            }
            invalidate();
        }
    }

    /**
     * 组 以一行为一组
     */
    private class Row {
        public int j;

        Row(int j) {
            this.j = j;
        }

        // 一行3个单元格
        public Cell[] cells = new Cell[TOTAL_COL];

        public void drawCells(Canvas canvas) {
            for (int i = 0; i < cells.length; i++) {
                if (cells[i] != null) {
                    cells[i].drawSelf(canvas);
                }
            }

        }
    }

    // 单元格
    private class Cell {
        public String num;
        public State state;
        /**
         * i = 行 j = 列
         */
        public int i;
        public int j;

        public Cell(String num, State state, int i, int j) {
            super();
            this.num = num;
            this.state = state;
            this.i = i;
            this.j = j;
        }

        // 绘制一个单元格 如果颜色需要自定义可以修改
        public void drawSelf(Canvas canvas) {
            float left = (mCellWidth * j + numberPadding * j);
            float top = (mCellHeight * i + numberPadding * i);
            float right = (mCellWidth * (j + 1) + numberPadding * j);
            float bottom = (mCellHeight * (i + 1) + numberPadding * i);
            RectF bgRectF = new RectF(left, top, right, bottom);

            switch (state) {
                case CLICK_NUM:
                    //通用背景
                    canvas.drawBitmap(numPressedBg, null, bgRectF, bgPaint);
                    //删除
                    if (TextUtils.equals(num, deleteStr)) {
                        canvas.drawBitmap(numDeletePressedBg, (float) ((j + 0.5) * mCellWidth + j * numberPadding - numDeletePressedBg.getWidth() / 2),
                                (float) ((i + 0.5) * mCellHeight + i * numberPadding - numDeletePressedBg.getHeight() / 2), mSrcPaint);
                    }
                    //重置
                    else if (TextUtils.equals(num, resetStr)) {
                        canvas.drawBitmap(numResetPressedBg, (float) ((j + 0.5) * mCellWidth + j * numberPadding - numResetPressedBg.getWidth() / 2),
                                (float) ((i + 0.5) * mCellHeight + i * numberPadding - numResetPressedBg.getHeight() / 2), mSrcPaint);
                    }
                    //数字
                    else {
                        mNumPaint.setColor(getResources().getColor(R.color.color_white, getContext().getTheme()));
                        canvas.drawText(num,
                                (float) ((j + 0.5) * mCellWidth + j * numberPadding - mNumPaint.measureText(num) / 2),
                                (float) ((i + 0.5) * mCellHeight + i * numberPadding + mNumPaint.measureText(num) / 2 + DisplayTypedValueUtil.dip2px(getContext(), 4)),
                                mNumPaint);
                    }
                    break;
                case DEFAULT_NUM:
                    //通用背景
                    canvas.drawBitmap(numNormalBg, null, bgRectF, bgPaint);
                    //删除
                    if (TextUtils.equals(num, deleteStr)) {
                        canvas.drawBitmap(numDeleteNormalBg, (float) ((j + 0.5) * mCellWidth + j * numberPadding - numDeleteNormalBg.getWidth() / 2),
                                (float) ((i + 0.5) * mCellHeight + i * numberPadding - numDeleteNormalBg.getHeight() / 2), mSrcPaint);
                    }
                    //重置
                    else if (TextUtils.equals(num, resetStr)) {
                        canvas.drawBitmap(numResetNormalBg, (float) ((j + 0.5) * mCellWidth + j * numberPadding - numResetNormalBg.getWidth() / 2),
                                (float) ((i + 0.5) * mCellHeight + i * numberPadding - numResetNormalBg.getHeight() / 2), mSrcPaint);
                    }
                    //数字
                    else {
                        mNumPaint.setColor(getResources().getColor(R.color.color_1a1a1a, getContext().getTheme()));
                        canvas.drawText(num,
                                (float) ((j + 0.5) * mCellWidth + j * numberPadding - mNumPaint.measureText(num) / 2),
                                (float) ((i + 0.5) * mCellHeight + i * numberPadding + mNumPaint.measureText(num) / 2 + DisplayTypedValueUtil.dip2px(getContext(), 4)),
                                mNumPaint);
                    }

                default:
                    break;
            }

        }
    }

    /**
     * cell的state
     */
    private enum State {
        DEFAULT_NUM, CLICK_NUM;
    }


    /**
     * 随机键盘
     *
     * @param isRandom
     */
    public void setRandomKeyBoard(boolean isRandom) {
        if (isRandom) {
            Collections.shuffle(numKeys);
            initData();
            invalidate();
        }
    }

    public Bitmap drawableToBitmap(Drawable drawable) {
        // 取 drawable 的长宽
        int w = (int) mCellWidth;//drawable.getIntrinsicWidth();
        int h = (int) mCellHeight;//drawable.getIntrinsicHeight();

        // 取 drawable 的颜色格式
        Bitmap.Config config = drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888
                : Bitmap.Config.RGB_565;
        // 建立对应 bitmap
        Bitmap bitmap = Bitmap.createBitmap(w, h, config);
        // 建立对应 bitmap 的画布
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, w, h);
        // 把 drawable 内容画到画布中
        drawable.draw(canvas);
        return bitmap;
    }


    public interface CallBack {
        void clickNum(String num);// 回调点击的数字

        void deleteNum();// 回调删除

        void resetNum(); //重置

    }


    public void setCallBack(CallBack callBack) {
        mCallBack = callBack;
    }

}
