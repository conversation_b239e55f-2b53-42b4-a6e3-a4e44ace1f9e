package com.jd.amr.hmi.model.data.entity;

import java.io.Serializable;

/**
 * 停靠点信息实体类
 * 用于表示机器人的停靠点信息
 */
public class ParkPoint implements Serializable {

    /**
     * 停靠站点ID
     */
    private String parkStationId;
    
    /**
     * 停靠站点类型
     */
    private String parkStationType;
    
    /**
     * 停靠站点类型名称
     */
    private String parkStationTypeName;
    
    /**
     * 停靠点ID
     */
    private String parkPointId;
    
    /**
     * 停靠点名称
     */
    private String parkPointName;

    /**
     * 默认构造函数
     */
    public ParkPoint() {
    }

    /**
     * 全参数构造函数
     * 
     * @param parkStationId 停靠站点ID
     * @param parkStationType 停靠站点类型
     * @param parkStationTypeName 停靠站点类型名称
     * @param parkPointId 停靠点ID
     * @param parkPointName 停靠点名称
     */
    public ParkPoint(String parkStationId, String parkStationType, String parkStationTypeName, String parkPointId, String parkPointName) {
        this.parkStationId = parkStationId;
        this.parkStationType = parkStationType;
        this.parkStationTypeName = parkStationTypeName;
        this.parkPointId = parkPointId;
        this.parkPointName = parkPointName;
    }

    /**
     * 获取停靠站点ID
     * 
     * @return 停靠站点ID
     */
    public String getParkStationId() {
        return parkStationId;
    }

    /**
     * 设置停靠站点ID
     * 
     * @param parkStationId 停靠站点ID
     */
    public void setParkStationId(String parkStationId) {
        this.parkStationId = parkStationId;
    }

    /**
     * 获取停靠站点类型
     * 
     * @return 停靠站点类型
     */
    public String getParkStationType() {
        return parkStationType;
    }

    /**
     * 设置停靠站点类型
     * 
     * @param parkStationType 停靠站点类型
     */
    public void setParkStationType(String parkStationType) {
        this.parkStationType = parkStationType;
    }

    /**
     * 获取停靠站点类型名称
     * 
     * @return 停靠站点类型名称
     */
    public String getParkStationTypeName() {
        return parkStationTypeName;
    }

    /**
     * 设置停靠站点类型名称
     * 
     * @param parkStationTypeName 停靠站点类型名称
     */
    public void setParkStationTypeName(String parkStationTypeName) {
        this.parkStationTypeName = parkStationTypeName;
    }

    /**
     * 获取停靠点ID
     * 
     * @return 停靠点ID
     */
    public String getParkPointId() {
        return parkPointId;
    }

    /**
     * 设置停靠点ID
     * 
     * @param parkPointId 停靠点ID
     */
    public void setParkPointId(String parkPointId) {
        this.parkPointId = parkPointId;
    }

    /**
     * 获取停靠点名称
     * 
     * @return 停靠点名称
     */
    public String getParkPointName() {
        return parkPointName;
    }

    /**
     * 设置停靠点名称
     * 
     * @param parkPointName 停靠点名称
     */
    public void setParkPointName(String parkPointName) {
        this.parkPointName = parkPointName;
    }

    @Override
    public String toString() {
        return "ParkPoint{" +
                "parkStationId='" + parkStationId + '\'' +
                ", parkStationType='" + parkStationType + '\'' +
                ", parkStationTypeName='" + parkStationTypeName + '\'' +
                ", parkPointId='" + parkPointId + '\'' +
                ", parkPointName='" + parkPointName + '\'' +
                '}';
    }
}
