/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jd.amr.hmi.model.data.mqtt;

/**
 * <p>
 * mqtt 响应头
 * </p >
 *
 * <AUTHOR>
 * @date： 2022/7/21
 */
public class MqttResponseHeader {

    private String messageType;

    //请求ID：13位毫秒时间戳+6位随机数字
    private Long requestId;

    private String sendName;

    private String receiveName;

    private Long requestTime;

    private Long responseTime;
    //消息状态：RUNNING,SUCCESS,FAIL
    private String messageState;
    //失败信息
    private String failInfo;

    public MqttResponseHeader(){}

    public MqttResponseHeader(String messageType, Long requestId, String robotSn, Long requestTime, Long responseTime, String messageState, String failInfo) {
        this.messageType = messageType;
        this.requestId = requestId;
        this.sendName = robotSn;
        this.requestTime = requestTime;
        this.responseTime = responseTime;
        this.messageState = messageState;
        this.failInfo = failInfo;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public Long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Long requestTime) {
        this.requestTime = requestTime;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public String getMessageState() {
        return messageState;
    }

    public void setMessageState(String messageState) {
        this.messageState = messageState;
    }

    public String getFailInfo() {
        return failInfo;
    }

    public void setFailInfo(String failInfo) {
        this.failInfo = failInfo;
    }

    @Override
    public String toString() {
        return "MqttResponseHeader{" +
                "messageType='" + messageType + '\'' +
                ", requestId=" + requestId +
                ", sendName='" + sendName + '\'' +
                ", receiveName='" + receiveName + '\'' +
                ", requestTime=" + requestTime +
                ", responseTime=" + responseTime +
                ", messageState='" + messageState + '\'' +
                ", failInfo='" + failInfo + '\'' +
                '}';
    }
}
