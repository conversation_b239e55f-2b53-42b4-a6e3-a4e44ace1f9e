package com.jd.amr.hmi.common;

import android.os.Environment;

public class Constants {

    public static String SP_KEY = "com.jd.amr";
    public static String androidIp = "*************";
    public static String rosIp = "*************";
    public static int port = 8765;
    public static String rosBridgeUri = "ws://" + rosIp + ":" + port;


    public static final String LOP_DN = "LOP-DN";

    //出发
    public static final String TRAVEL_START= "start";

    //视同到达
    public static final String TRAVEL_STOP = "stop";

    //业务到达
    public static final String TRAVEL_END= "end";

    public static final String TOKEN = "authentication/getClientToken";

    public final static String FOLDER_UPDATE = "/update/";

    public final static String TOTAL_SIZE = "total_size";

    public final static String DOWNLOADED_SIZE = "downloaded_size";

    public final static String OTA_TASK_INFO = "ota_task_info";

    //运管OTA界面应用的英文名
    public final static String APP_NAME = "integrate_hmi";

    public final static String KEY_OTA_STATUS = "ota_status";

    public final static String KEY_OTA_VERSION = "ota_version";

    public final static String ROBOT_DEBUG_CONFIG = "/sdcard/robot_debug_config.json";

    public final static String ROBOT_LOG_PATH = "/sdcard/sysLog";
}
