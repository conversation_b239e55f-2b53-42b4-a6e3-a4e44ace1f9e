package com.jd.amr.hmi.model.data.http;

import com.google.gson.Gson;

public class ShelfRequestBody extends VehicleRequestBody{

    /**
     * 上装编码
     */
    private String shelfNo;

    public ShelfRequestBody(String robotSn, String shelfNo) {
        super(robotSn);
        this.shelfNo = shelfNo;
    }

    @Override
    public String makeRequestBody() {
        return new Gson().toJson(this);
    }
}
