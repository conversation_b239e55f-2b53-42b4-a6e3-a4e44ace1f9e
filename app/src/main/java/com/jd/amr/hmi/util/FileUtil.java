/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jd.amr.hmi.util;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.jd.ugv.pad.common.utils.LogUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class FileUtil {
    private static final String TAG = "FileUtil";

    /**
     * 获取指定路径下的文件列表，并根据文件名过滤
     *
     * @param path           指定的路径
     * @param fileNameFilter 文件名过滤器，可以为null
     * @return 文件列表，如果路径不存在或不是目录则返回空列表
     */
    public static List<File> getFilesInDirectory(String path, String fileNameFilter) {
        List<File> result = new ArrayList<>();
        if (TextUtils.isEmpty(path)) {
            return result;
        }
        File directory;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上，使用MediaStore API或者SAF来访问外部存储
            if (Environment.isExternalStorageManager()) {
                directory = new File(path);
            } else {
                // 如果没有所有文件访问权限，可以考虑使用MediaStore API或者请求权限
                Log.e(TAG, "没有所有文件访问权限，无法访问: " + path);
                return result;
            }
        } else {
            directory = new File(path);
        }

        if (!directory.exists() || !directory.isDirectory()) {
            return result;
        }
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (TextUtils.isEmpty(fileNameFilter) || file.getName().contains(fileNameFilter)) {
                    result.add(file);
                }
            }
        }

        return result;
    }

    public static void writeInFile(String path, String content) {
        try {
            File file = new File(path);
            if (file.exists() == false) {
                try {
                    file.createNewFile();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            FileOutputStream fos = null;
            fos = new FileOutputStream(file);
            fos.write(content.getBytes("gbk"));
            fos.flush();
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String readFromFile(String path) {
        FileInputStream in = null;
        BufferedReader reader = null;
        StringBuilder content = new StringBuilder();
        try {
            File file = new File(path);
            if (file.exists() == false) {
                return null;
            }

            in = new FileInputStream(file);
            reader = new BufferedReader(new InputStreamReader(in));
            String line = "";
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return content.toString();
    }

    public static void copyFile(File source, File dest, long position, long count) throws IOException {
        FileChannel inputChannel = null;
        FileChannel outputChannel = null;
        try {
            inputChannel = new FileInputStream(source).getChannel();
            outputChannel = new FileOutputStream(dest).getChannel();
            inputChannel.transferTo(position, count, outputChannel);
        } finally {
            inputChannel.close();
            outputChannel.close();
        }
    }


    public static long getContentPosition(File file, String content) {
        long position = 0l;
        long startTime = System.currentTimeMillis();
        boolean matched = false;
        try {
            RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r");
            BufferedReader brRafReader = new BufferedReader(
                    new FileReader(randomAccessFile.getFD()));
            String line = null;
            while ((line = brRafReader.readLine()) != null) {
                if (matched) {
                    position = randomAccessFile.getFilePointer();
                    break;
                }
                if ((line.startsWith(content))) {
                    matched = true;
                }
            }
        } catch (IOException e) {
        }

        Log.d(TAG, "getContentPosition cost:" + (System.currentTimeMillis() - startTime));
        return matched ? position : -1;
    }

    public static long getFolderSize(File file) {
        long size = 0;
        try {
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isDirectory()) {
                    size = size + getFolderSize(files[i]);
                } else {
                    size = size + files[i].length();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        float newSize = (float) size / 1024.0f / 1024.0f;
        return size;
    }

    public static void deleteFolderFile(String filePath, boolean isDelete) {
        if (!TextUtils.isEmpty(filePath)) {
            try {
                File file = new File(filePath);
                if (file.isDirectory()) {
                    File file1[] = file.listFiles();
                    for (int i = 0; i < file1.length; i++) {
                        deleteFolderFile(file1[i].getAbsolutePath(), isDelete);
                    }
                }
                if (isDelete) {
                    if (!file.isDirectory()) {
                        file.delete();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static final int SIZETYPE_B = 1;//获取文件大小单位为B的double值
    public static final int SIZETYPE_KB = 2;//获取文件大小单位为KB的double值
    public static final int SIZETYPE_MB = 3;//获取文件大小单位为MB的double值
    public static final int SIZETYPE_GB = 4;//获取文件大小单位为GB的double值

    /**
     * 获取文件指定文件的指定单位的大小
     *
     * @param filePath 文件路径
     * @return double值的大小
     */
    public static String getFileOrFilesSize(String filePath) {
        File file = new File(filePath);
        long blockSize = 0;
        try {
            if (file.isDirectory()) {
                blockSize = getFileSizes(file);
            } else {
                blockSize = getFileSize(file);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return FormetFileSize(blockSize);
    }

    /**
     * 调用此方法自动计算指定文件或指定文件夹的大小
     *
     * @param filePath 文件路径
     * @return 计算好的带B、KB、MB、GB的字符串
     */
    public static String getAutoFileOrFilesSize(String filePath) {
        File file = new File(filePath);
        long blockSize = 0;
        try {
            if (file.isDirectory()) {
                blockSize = getFileSizes(file);
            } else {
                blockSize = getFileSize(file);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return FormetFileSize(blockSize);
    }

    /**
     * 获取指定文件大小
     *
     * @param file
     * @return
     * @throws Exception
     */
    private static long getFileSize(File file) throws Exception {
        long size = 0;
        if (file.exists()) {
            FileInputStream fis = null;
            fis = new FileInputStream(file);
            size = fis.available();
        } else {
            file.createNewFile();
        }
        return size;
    }

    /**
     * 获取指定文件夹
     *
     * @param f
     * @return
     * @throws Exception
     */
    private static long getFileSizes(File f) throws Exception {
        long size = 0;
        File flist[] = f.listFiles();
        for (int i = 0; i < flist.length; i++) {
            if (flist[i].isDirectory()) {
                size = size + getFileSizes(flist[i]);
            } else {
                size = size + getFileSize(flist[i]);
            }
        }
        return size;
    }

    /**
     * 转换文件大小
     *
     * @param fileS
     * @return
     */
    private static String FormetFileSize(long fileS) {
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        String wrongSize = "0B";
        if (fileS == 0) {
            return wrongSize;
        }
        if (fileS < 1024) {
            fileSizeString = df.format((double) fileS) + "B";
        } else if (fileS < 1048576) {
            fileSizeString = df.format((double) fileS / 1024) + "KB";
        } else if (fileS < 1073741824) {
            fileSizeString = df.format((double) fileS / 1048576) + "MB";
        } else {
            fileSizeString = df.format((double) fileS / 1073741824) + "GB";
        }
        return fileSizeString;
    }

    /**
     * 转换文件大小,指定转换的类型
     *
     * @param fileS
     * @param sizeType
     * @return
     */
    private static double FormetFileSize(long fileS, int sizeType) {
        DecimalFormat df = new DecimalFormat("#.00");
        double fileSizeLong = 0;
        switch (sizeType) {
            case SIZETYPE_B:
                fileSizeLong = Double.valueOf(df.format((double) fileS));
                break;
            case SIZETYPE_KB:
                fileSizeLong = Double.valueOf(df.format((double) fileS / 1024));
                break;
            case SIZETYPE_MB:
                fileSizeLong = Double.valueOf(df.format((double) fileS / (1024 * 1024)));
                break;
            case SIZETYPE_GB:
                fileSizeLong = Double.valueOf(df.format((double) fileS / (1024 * 1024 * 1024)));
                break;
            default:
                break;
        }
        return fileSizeLong;
    }


    /**
     * 将文件集合打包压缩，并用车号和时间命名
     *
     * @param files
     * @param fileName
     * @param outputPath
     * @return
     */
    public static File compressFiles(List<File> files, String fileName, String outputPath) {
        String zipFileName = fileName + ".zip";
        File zipFile = new File(outputPath, zipFileName);
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            for (File file : files) {
                if (file.exists()) {
                    ZipEntry zipEntry = new ZipEntry(file.getName());
                    zos.putNextEntry(zipEntry);

                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, length);
                        }
                        zos.closeEntry();
                    }
                }
            }
            return zipFile;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

}
