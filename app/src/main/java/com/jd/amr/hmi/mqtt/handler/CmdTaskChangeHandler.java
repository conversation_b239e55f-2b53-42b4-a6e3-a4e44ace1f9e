package com.jd.amr.hmi.mqtt.handler;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.impl.model.TaskDataModel;
import com.jd.amr.hmi.manager.http.impl.model.TokenDataModel;
import com.jd.amr.hmi.model.data.entity.TaskInfo;
import com.jd.amr.hmi.model.data.event.TaskChangeEvent;
import com.jd.amr.hmi.model.data.mqtt.MqttResponseData;
import com.jd.amr.hmi.model.enums.RemoteCmdType;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.TokenData;
import com.jd.ugv.pad.common.base.TokenInfoData;
import com.jd.ugv.pad.common.utils.LogUtils;

import org.greenrobot.eventbus.EventBus;

import java.lang.reflect.Type;

/**
 * 任务变更指令处理器
 */
public class CmdTaskChangeHandler implements ICmdHandler {

    private static final String TAG = "CmdTaskChangeHandler";

    private TaskDataModel mTaskDataModel;
    private TokenDataModel mTokenDataModel;

    @Override
    public void handleMsg(Context context, String msg) {
        LogUtils.i(TAG, "handleTaskChangeData===>data: " +msg);
        if(GlobalRobotDataManager.getInstance().getTokenData() != null){
            getTaskInfo();
        }else{
            LogUtils.i(TAG, "token is null");
            getTokenInfo();
        }



    }

    /**
     * 获取token信息
     */
    private void getTokenInfo() {
        if (mTokenDataModel == null) {
            mTokenDataModel = new TokenDataModel();
        }
        String robotSn = GlobalRobotDataManager.getInstance().getRobotSn();
        String apiKey = GlobalRobotDataManager.getInstance().getApiKey();
        if(robotSn != null && apiKey != null){
            mTokenDataModel.getTokenInfo(robotSn, apiKey, new BaseCallBack() {
                @Override
                public void success(BaseResult baseResult) {
                    TokenInfoData tokenInfoData = (TokenInfoData) baseResult.getData();
                    if (tokenInfoData != null) {
                        TokenData tokenData = new TokenData(tokenInfoData.getAccessToken(), robotSn, tokenInfoData.getExpiresIn(), BuildConfig.LOP_DN);
                        tokenData.setRobotSn(robotSn);
                        tokenData.setApkKey(apiKey);
                        tokenData.setUrl(BuildConfig.PROXY_URL + Constants.TOKEN);
                        GlobalRobotDataManager.getInstance().setTokenData(tokenData);
                        getTaskInfo();
                    }
                }

                @Override
                public boolean failOrError(BaseResult baseResult, String e) {
                    LogUtils.e(TAG, "getTokenInfo failOrError: " + e);
                    return false;
                }
            });
        }
    }

    /**
     * 获取任务信息
     */
    private void getTaskInfo(){
        if(mTaskDataModel == null){
            mTaskDataModel = new TaskDataModel();
        }
        mTaskDataModel.getTaskInfo(new BaseCallBack<TaskInfo>() {
            @Override
            public void success(BaseResult<TaskInfo> baseResult) {
                TaskInfo taskInfo = baseResult.getData();
                if(taskInfo != null){
                    //TOOD 任务信息变更
                }
            }

            @Override
            public boolean failOrError(BaseResult<TaskInfo> baseResult, String e) {
                return false;
            }
        });
    }

    @Override
    public String cmdType() {
        return RemoteCmdType.CMD_TASK_CHANGE.getType();
    }
}
