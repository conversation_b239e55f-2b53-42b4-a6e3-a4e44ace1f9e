plugins {
    id 'com.android.application'
}

static def releaseTime() {
    return new Date().format("yyyyMMddHHmmss", TimeZone.getTimeZone("GMT+08:00"))
}

android {
    namespace "com.jd.amr.hmi"
    compileSdk rootProject.ext.android.compileSdkVersion

    defaultConfig {
        applicationId rootProject.ext.android.applicationId
        minSdk rootProject.ext.android.minSdkVersion
        targetSdk rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        flavorDimensions "default"
    }


    signingConfigs {
        release {
            keyAlias 'keyAlias'
            keyPassword 'android'
            storeFile file('../keystore/jd-car.keystore')
            storePassword 'android'
        }
        debug {
            keyAlias 'keyAlias'
            keyPassword 'android'
            storeFile file('../keystore/jd-car.keystore')
            storePassword 'android'
        }

    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "jd_amr_app_${defaultConfig.versionName}_${defaultConfig.versionCode}_${releaseTime()}_${variant.buildType.name}.apk"
        }
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
            buildConfigField "boolean", "DEBUGABLE", "true"
        }

        release {
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "DEBUGABLE", "false"
        }
    }

    productFlavors{
        // 开发环境 / 测试环境
        beta{
            buildConfigField "String", "PROXY_URL", '"https://uat-api-cloud.jdl.cn/"'
            buildConfigField "String", "MQTT_URL", '"ssl://jdxmqtt-beta.jdl.com:2000"'
            buildConfigField "String", "MQTT_ENV", '"beta"'
            buildConfigField "String", "LOP_DN", '"jdx.integrate.android.jsf.beta"'
        }


        // 线上环境
        online {
            buildConfigField "String", "PROXY_URL", '"https://api-lop.jdl.cn/"'
            buildConfigField "String", "MQTT_URL", '"ssl://jdxmqtt.jdl.com:2000"'
            buildConfigField "String", "MQTT_ENV", '"product"'
            buildConfigField "String", "LOP_DN", '"jdx.integrate.android.jsf.product"'
        }

        // 测试环境1
        jdTest1 {
            buildConfigField "String", "PROXY_URL", '"https://uat-api-cloud.jdl.cn/"'
            buildConfigField "String", "MQTT_URL", '"ssl://jdxmqtt-beta.jdl.com:2000"'
            buildConfigField "String", "MQTT_ENV", '"test1"'
            buildConfigField "String", "LOP_DN", '"jdx.integrate.android.jsf.test1"'
        }

    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    packagingOptions {
        exclude 'META-INF/INDEX.LIST'
    }
    buildFeatures {
        viewBinding true
        buildConfig true
    }
}

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(":ros2-rcljava")
    implementation project(":lib_http")
    implementation rootProject.ext.dependencies["retrofit2"]
    implementation rootProject.ext.dependencies["retrofit2-adapter"]
    implementation rootProject.ext.dependencies["mqtt-android"]
    implementation rootProject.ext.dependencies["mqtt-service"]
    implementation rootProject.ext.dependencies["zxing"]
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
    implementation rootProject.ext.dependencies["eventbus"]
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}