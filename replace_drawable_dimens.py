#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

def replace_dimens_in_drawable_file(file_path):
    """替换drawable文件中的dp值"""
    
    # 定义需要替换的dp值（包含drawable中发现的新值）
    dp_values = [787, 760, 659, 617, 616, 582, 573, 510, 433, 400, 380, 370, 368, 351, 350, 348, 345, 336, 335, 261, 250, 224, 216, 203, 200, 186, 160, 157, 155, 147, 145, 144, 140, 124, 122, 120, 111, 108, 107, 100, 96, 88, 87, 80, 75, 74, 73, 72, 64, 60, 56, 52, 50, 48, 44, 42, 40, 36, 32, 31, 30, 26, 25, 24, 22, 21, 20, 18, 17, 16, 15, 14, 12, 10, 8, 6, 5, 3, 2, 1]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换dp值（按长度降序，避免部分匹配）
        for dp in dp_values:
            pattern = r'\b' + str(dp) + r'dp\b'
            replacement = f'@dimen/dp_{dp}'
            content = re.sub(pattern, replacement, content)
        
        # 只有内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已处理: {file_path}")
            return True
        else:
            print(f"- 无变化: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 错误处理 {file_path}: {e}")
        return False

def show_file_preview(file_path, lines=10):
    """显示文件预览"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines_content = f.readlines()[:lines]
            for i, line in enumerate(lines_content, 1):
                print(f"{i:2d}: {line.rstrip()}")
    except Exception as e:
        print(f"读取文件错误: {e}")

def main():
    """主函数"""
    print("开始替换drawable文件中的dp值...")
    
    # 获取所有drawable文件
    drawable_files = glob.glob('app/src/main/res/drawable/*.xml')
    
    if not drawable_files:
        print("未找到drawable文件")
        return
    
    print(f"找到 {len(drawable_files)} 个drawable文件:")
    for file_path in drawable_files:
        print(f"  - {os.path.basename(file_path)}")
    
    print("\n开始处理...")
    
    processed_count = 0
    total_count = len(drawable_files)
    
    for file_path in drawable_files:
        if replace_dimens_in_drawable_file(file_path):
            processed_count += 1
    
    print(f"\n替换完成！")
    print(f"总文件数: {total_count}")
    print(f"已处理文件数: {processed_count}")
    print(f"未变化文件数: {total_count - processed_count}")
    
    # 显示几个示例文件的内容
    print("\n验证替换结果:")
    processed_files = [f for f in drawable_files if os.path.exists(f)]
    for file_path in processed_files[:3]:  # 显示前3个文件
        print(f"\n=== {os.path.basename(file_path)} ===")
        show_file_preview(file_path, 15)
    
    # 显示所有drawable文件中的dimen引用
    print(f"\n检查所有drawable文件中的dimen引用:")
    for file_path in drawable_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                dimen_refs = re.findall(r'@dimen/dp_\d+', content)
                if dimen_refs:
                    print(f"{os.path.basename(file_path)}: {', '.join(set(dimen_refs))}")
        except Exception as e:
            print(f"检查 {file_path} 时出错: {e}")

if __name__ == "__main__":
    main()
