package com.iscas.rcljava.socket

import RosMsg.msg.ImuHelper
import RosMsg.srv.SetBool_Request
import RosMsg.srv.SetBool_RequestHelper
import RosMsg.srv.SetBool_Response
import RosMsg.srv.SetBool_ResponseHelper
import android.util.Log
import android.util.LogPrinter
import com.alibaba.fastjson.JSON
import com.iscas.rcljava.entity.RobotMsg
import com.iscas.rcljava.entity.type.NameAndType
import com.iscas.rcljava.handle.ActionStateHandleThread
import com.iscas.rcljava.handle.ConsumerBlockingCache
import com.iscas.rcljava.handle.LiteDataHandleThread
import com.iscas.rcljava.handle.PngDataHandleThread
import com.iscas.rcljava.handle.ServiceRespHandleThread
import com.iscas.rcljava.handle.ServiceTimeoutConcurrentHashMap
import com.iscas.rcljava.handle.TopicTimeoutConcurrentHashMap
import com.iscas.rcljava.internal.RosServiceExecutor
import com.iscas.rcljava.internal.ServiceRunnable
import com.iscas.rcljava.robot.AbsMessageListener
import com.iscas.rcljava.robot.Service
import com.iscas.rcljava.robot.Topic
import com.iscas.rcljava.socket.ROSClient.MessageListener
import com.iscas.rcljava.utils.LogUtils
import org.jacorb.orb.CDRInputStream
import org.jacorb.orb.CDROutputStream
import org.jacorb.orb.ORB
import org.java_websocket.exceptions.WebsocketNotConnectedException
import org.omg.CORBA.portable.IDLEntity
import org.omg.CORBA.portable.OutputStream
import java.net.InetSocketAddress
import java.net.URI
import java.net.URISyntaxException
import java.nio.ByteBuffer
import java.nio.channels.NotYetConnectedException
import java.util.Arrays
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import java.util.logging.Logger

open class ROSClientManager : ConnectionStatusListener, MessageListener {
    private var rosTopics = mutableSetOf<Advertise>()
    private var fullReceives = mutableListOf<Int>()
    private var rosServices = mutableSetOf<ServiceInfo>()
    private var client: ROSBridgeClient? = null
    private val topicObserver = TopicTimeoutConcurrentHashMap()
    private val serviceObserver = ServiceTimeoutConcurrentHashMap()
    private var listener: ConnectionStatusListener? = null
    private val pngDataCache = ConsumerBlockingCache<String>()
    private val liteDataCache: ConsumerBlockingCache<ByteArray> = ConsumerBlockingCache<ByteArray>()
    private val respDataCache = ArrayBlockingQueue<ByteArray>(1000)
    private val actionDataCache = ArrayBlockingQueue<ByteArray>(1000)
    private var ready = false

    init {
        val pngThread = PngDataHandleThread(
            "png_h_t",
            pngDataCache,
            topicObserver,
            serviceObserver
        )
        pngThread.start()
        val liteThread = LiteDataHandleThread(
            "lite_h_t",
            liteDataCache,
            topicObserver
        )
        liteThread.start()
        val serverRespThread = ServiceRespHandleThread(
            "resp_h_t",
            respDataCache,
            topicObserver,
            serviceObserver
        )
        serverRespThread.start()

        val actionStateHandleThread = ActionStateHandleThread(
            "action_h_t",
            actionDataCache,
            topicObserver
        )
        actionStateHandleThread.start()
    }

    fun setDebug(debug: Boolean) {
        if (client != null) {
            client!!.setDebug(debug)
        }
    }

    @Throws(URISyntaxException::class)
    fun connect(listener: ConnectionStatusListener, src: InetSocketAddress?, uri: String) {
        var src = src
        this.listener = listener
        if (src == null) {
            src = InetSocketAddress("**************", 18001)
        }
        initSocketConnect(src, uri)
    }

    fun disconnect() {
        if (client != null) client!!.disconnect()
    }

    @Throws(URISyntaxException::class)
    private fun initSocketConnect(src: InetSocketAddress, uri: String) {
        client = ROSBridgeClient(src, URI(uri), httpHeader = mapOf("sec-websocket-protocol" to "foxglove.websocket.v1"))
        client!!.connect(this, this)
    }

    fun send(json: String) {
        client!!.send(json)
    }

    fun send(data: ByteArray) {
        client!!.send(data)
    }

    protected  fun <Request: IDLEntity, Response: IDLEntity> service(serviceName: String, request: Request, notify: AbsMessageListener<Response>) {
        var service = rosServices.find { it.name == serviceName }
        if (service == null) {
            Log.d("ROS","can not find service:$serviceName")
            notify.onError(-100,"can not find service:$serviceName")
            return
        }
        var ost: CDROutputStream? = null
        try {
            ost = CDROutputStream()
            var clazzName = request.javaClass.name

            val clazz = Class.forName("${clazzName}Helper")
            val write = clazz.getMethod("write", OutputStream::class.java, request::class.java)
            write.invoke(null, ost, request)
            Log.d("ROS","name:${service.name} id:${notify.hashCode()} type:${clazzName} data:${JSON.toJSONString(request)}")
            callService(service!!, ost.bufferCopy, notify)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }finally {
            ost?.close()
        }
    }

    protected fun callService(service: ServiceInfo, data: ByteArray, notify: AbsMessageListener<*>) {
        try {
            var callId = notify.hashCode()
            serviceObserver.add(service.id, callId, notify)
            var payload = payloadAlign(data)
//            Log.d("ROS", "request payload: callId:$callId ${Arrays.toString(payload)}")

            var buf = ByteArray(1 + 4 + 4 + 4 + "cdr".length + payload.size)
            var serviceId = service!!.id.toInt()
            buf[0] = 2
            buf[4] = ((serviceId shr 24) and 0xFF).toByte()
            buf[3] = ((serviceId shr 16) and 0xFF).toByte()
            buf[2] = ((serviceId shr 8) and 0xFF).toByte()
            buf[1] = (serviceId and 0xFF).toByte()

            buf[8] = ((callId shr 24) and 0xFF).toByte()
            buf[7] = ((callId shr 16) and 0xFF).toByte()
            buf[6] = ((callId shr 8) and 0xFF).toByte()
            buf[5] = (callId and 0xFF).toByte()

            var encoding = "cdr".encodeToByteArray()
            var encodingLength = encoding.size
            buf[12] = ((encodingLength shr 24) and 0xFF).toByte()
            buf[11] = ((encodingLength shr 16) and 0xFF).toByte()
            buf[10] = ((encodingLength shr 8) and 0xFF).toByte()
            buf[9] = (encodingLength and 0xFF).toByte()

            System.arraycopy(encoding, 0, buf, 13, encoding.size)
            System.arraycopy(payload, 0, buf, 13 + encoding.size, payload.size)
//            Log.d("kang","buf:${Arrays.toString(buf)} ${ByteUtil.ByteArrToHex(buf)}")
            send(buf)
        } catch (e: NotYetConnectedException) {
            e.printStackTrace()
            notify.onError(1, if (e.message == null) "NotYetConnectedException" else e.message)
            serviceObserver.remove(service.id)
        } catch (e: WebsocketNotConnectedException) {
            e.printStackTrace()
            notify.onError(1, if (e.message == null) "NotYetConnectedException" else e.message)
            serviceObserver.remove(service.id)
        }
    }

    private fun payloadAlign(payload: ByteArray): ByteArray {
        var prefix = byteArrayOf(0, 0, 0, 0)
        if (payload.size % 4 == 0) {
            var payloadA = ByteArray(payload.size + 4)
            System.arraycopy(prefix, 0, payloadA, 0, prefix.size)
            System.arraycopy(payload, 0, payloadA, prefix.size, payload.size)
            return payloadA
        } else {
            var payloadA = ByteArray(payload.size + 4 - payload.size % 4 + 4)
            System.arraycopy(prefix, 0, payloadA, 0, prefix.size)
            System.arraycopy(payload, 0, payloadA, prefix.size, payload.size)
            return payloadA
        }
    }

    protected fun <T: IDLEntity> publish(topicName: String, request: T){
        var filters = rosTopics.filter { it.topic == topicName }.sortedByDescending { it.id }
        if (filters.isEmpty()) {
            Log.d("ROS","publish failed ${topicName} not exist")
            return
        }
        var topic = filters.first()
        if (topic == null) {
            Log.d("ROS","publish failed ${topicName} not exist")
            return
        }
        var ost: CDROutputStream? = null
        try {
            ost = CDROutputStream()
            var clazzName = request.javaClass.name

            val clazz = Class.forName("${clazzName}Helper")
            val write = clazz.getMethod("write", OutputStream::class.java, request::class.java)
            write.invoke(null, ost, request)
//            Log.d("ROS","publish name:${topicName} type:${clazzName} data:${JSON.toJSONString(request)}")
            var payload = payloadAlign(ost.bufferCopy)
            var buf = ByteArray(1 + 4 + payload.size)
            var channelId = topic.id
            buf[0] = 1
            buf[4] = ((channelId shr 24) and 0xFF).toByte()
            buf[3] = ((channelId shr 16) and 0xFF).toByte()
            buf[2] = ((channelId shr 8) and 0xFF).toByte()
            buf[1] = (channelId and 0xFF).toByte()

            System.arraycopy(payload, 0, buf, 5, payload.size)
            Log.d("ROS","sen buf:${Arrays.toString(buf)}")
            send(buf)
        } catch (e: NotYetConnectedException) {
            e.printStackTrace()
        } catch (e: WebsocketNotConnectedException) {
            e.printStackTrace()
        }catch (e: java.lang.Exception) {
            e.printStackTrace()
        }finally {
            ost?.close()
        }
    }

    protected fun <T> subscribeInternal(topicName: String, listener: AbsMessageListener<T>) {
        rosTopics.find { it.topic == topicName }?.let {
            subscribe(it, listener)
            Log.d("ROS","subscribe:${topicName}")
        }?: run {
            listener.onError(-100, "${topicName} not exist")
        }
    }

    //    private void subscribe(Topic topic, AbsMessageListener notify) {
    //        subscribe(topic, 0, 0, null, notify);
    //    }
    private fun <T> subscribe(topic: Advertise, notify: AbsMessageListener<T>) {
        try {
            var sub = SubscribeFoxglove(subscriptions = listOf(Subscription(topic.id, topic.id)))
            if (topicObserver.add(topic.id.toString(), notify)) {
                client!!.send(sub.toString())
            }
        } catch (e: NotYetConnectedException) {
            e.printStackTrace()
            topicObserver.removeTopic(topic.id.toString(), notify)
            notify.onError(1, if (e.message == null) "NotYetConnectedException" else e.message)
        } catch (e: WebsocketNotConnectedException) {
            e.printStackTrace()
            topicObserver.removeTopic(topic.id.toString(), notify)
            notify.onError(1, if (e.message == null) "NotYetConnectedException" else e.message)
        }
    }

    protected fun unsubscribeInternal(topic: String, notify: AbsMessageListener<*>?) {
        rosTopics.find { it.topic == topic }?.let {
            unsubscribe(it, notify?.back)
            Log.d("ROS","unsubscribe:${topic}")
        }
    }

    private fun unsubscribe(topic: Advertise, notify: AbsMessageListener<*>?) {
        if (notify == null) unsubscribe(topic.id)
        else if (topicObserver.removeTopic(topic.id.toString(), notify)) {
            unsubscribe(topic.id)
        }
    }

    /**
     * 取消订阅
     * @param name 主题名
     */
    fun unsubscribe(id: Int) {
        val json = UnsubscribeFoxglove(listOf(id))
        try {
            client!!.send(json.toString())
        } catch (e: NotYetConnectedException) {
            e.printStackTrace()
        } catch (e: WebsocketNotConnectedException) {
            e.printStackTrace()
        } finally {
            if (topicObserver.containsKey(id.toString())) topicObserver.removeTopic(id.toString())
        }
    }

    fun unsubscribeAll() {
        val iterator: Iterator<String> = topicObserver.keys.iterator()
        while (iterator.hasNext()) {
            val next = iterator.next()
            unsubscribe(next.toInt())
        }
    }

    protected fun listTopicNameAndTypeInternal(): List<NameAndType> {
        return rosTopics.map {
            NameAndType(name = it.topic, type = it.schemaName?:"")
        }
    }

    protected fun listServiceNameAndTypeInternal(): List<NameAndType> {
        return rosServices.map {
            NameAndType(name = it.name, type = it.type)
        }
    }

    override fun onConnect() {
//        listener!!.onConnect()
    }

    override fun onDisconnect(normal: Boolean, reason: String, code: Int) {
        rosTopics.clear()
        rosServices.clear()
        fullReceives.clear()
        ready = false
        listener?.onDisconnect(normal, reason, code)
    }

    override fun onError(ex: Exception) {
        ex.printStackTrace()
        listener?.onError(ex)
    }


    override fun onMessage(buffer: ByteBuffer) {
        var start = System.currentTimeMillis()
        buffer.position(0)
        val firstByte = buffer.get()
//        Log.d("ROS","First byte: $firstByte ${buffer.array().size} ${buffer.array().hashCode()} ${Arrays.toString(buffer.array())}")
        if (firstByte == 3.toByte()) {
//            Log.d("ROS","service resp:${Arrays.toString(buffer.array())}")
            respDataCache.offer(buffer.array())
        } else if (firstByte == 1.toByte()) {
            // 读取第 2 到第 5 个字节
            buffer.position(1) // 设置 position 到第 2 个字节
            buffer.limit(5) // 设置 limit 到第 5 个字节
            val buf = ByteArray(buffer.remaining())
            buffer[buf]
//            Log.d("kang", "Bytes 2 to 5: ${Arrays.toString(buf)}")
            val subscriptionId = (buf[0].toInt() and 0xFF) or
                    ((buf[1].toInt() and 0xFF) shl 8) or
                    ((buf[2].toInt() and 0xFF) shl 16) or
                    ((buf[3].toInt() and 0xFF) shl 24)

            if (subscriptionId in fullReceives) {
                actionDataCache.offer(buffer.array())
            }else {
                liteDataCache.offer(buffer.array())
            }
//            buffer.limit(13); // 设置 limit 到第 12 个字节
//            buffer.position(5); // 设置 position 到第 6 个字节
//            val bytes6to12 = ByteArray(buffer.remaining());
//            buffer[bytes6to12]
//            Log.d("kang", "Bytes 6 to 12: " + Arrays.toString(bytes6to12));
//
//
//            buffer.limit(17); // 设置 limit 到第 12 个字节
//            buffer.position(13); // 设置 position 到第 6 个字节
//            val bytes13to16 = ByteArray(buffer.remaining());
//            buffer[bytes13to16]
//            Log.d("kang", "Bytes 13 to 16: " + Arrays.toString(bytes13to16));
//
//            buffer.limit(buffer.capacity()) // 设置 limit 到缓冲区的容量
//            // 恢复 position 和 limit
//            buffer.position(17) // 设置 position 到第 13 个字节
//            val remainingBytes = ByteArray(buffer.remaining())
//            buffer[remainingBytes]
//            Log.d("kang", "Remaining bytes: " + remainingBytes.contentToString())

//            var inp = CDRInputStream(orb, remainingBytes, true)
//            var action = ImuHelper.read(inp)
//            Log.d("kang", "${System.currentTimeMillis() - start}")
//            Log.d("kang", "${JSON.toJSONString(action)}")
        }
    }

    private fun checkTopicAndServiceReady() {
        if (ready) return
        if (rosServices.isNotEmpty() && rosServices.isNotEmpty()) {
            ready = true
            publishAdvertise()
            listener?.onConnect()
        }
    }

    private fun publishAdvertise() {
        var publishes = Topic.values().filter { it.publish }
        var first = rosTopics.sortedByDescending { it.id }.first()
        Log.d("ROS","publish advertise:${publishes.map { it.topic }} start with:${first.id}")
        for ((index,value) in publishes.withIndex()) {
            var channels = AdvertiseChannels(channels = mutableListOf())
            var advertise = Advertise(encoding = "cdr", id = first.id + index + 1, schemaEncoding = "ros2msg", schema = "", topic = value.topic, schemaName = value.type)
            channels.channels.add(advertise)
            rosTopics.add(advertise)
            send(channels.toString())
        }
    }

    override fun onMessage(message: String) {
        var start = System.currentTimeMillis()
        val topicBean = JSON.parseObject(message, BaseBean::class.java)
        val op = topicBean.op
        val parseTime = System.currentTimeMillis()
        Log.d("kang", "op:$op ${parseTime - start}")
        Log.d("kang", "message:$message")
        if ("advertise" == op) {
            start = System.currentTimeMillis()
            var topicInfo = JSON.parseObject(message, RosTopics::class.java)
            rosTopics.addAll(topicInfo.channels.filter { a ->
                Topic.values().find { it.topic == a.topic}?.publish == false
            }.also {
                Log.d("ROS","${it.map { "${it.id} ${it.topic}" }.joinToString(",")}")
            }).also {
                Log.d("ROS","ros topics size:${rosTopics.size}")
            }
            //不能丢的消息需要在这里处理
            fullReceives = rosTopics.filter { it.topic == Topic.TOPIC_ACTION_STATE.topic
                    || it.topic == Topic.TOPIC_ROBOT_TASK_INFO.topic }.also { Log.d("ROS", "full receive: ${it.joinToString(",")}") }.map { it.id } as MutableList<Int>
            Log.d("kang", "advertis:${topicInfo.channels.size} ${System.currentTimeMillis() - start}")
            checkTopicAndServiceReady()
        } else if ("unadvertise" == op) {
            var unadvertises = JSON.parseObject(message, Unadvertise::class.java).also {
                Log.d("ROS","unadvertise:${message}")
            }
            unadvertises.channelIds.forEach {  un ->
                rosTopics.find { it.id ==  un}?.let { topic ->
                    rosTopics.remove(topic)
                    Log.d("ROS","topics remove:${topic}")
                }
            }
            Log.d("ROS","ros topics size:${rosTopics.size}")
        } else if ("advertiseServices" == op) {
            start = System.currentTimeMillis()
            var service = JSON.parseObject(message, RosServices::class.java)
            Log.d("ROS","ros service info:${service}")
            var needServices = Service.values()
            rosServices.addAll(service.services.filter { s ->
                needServices.find { it.serviceName == s.name } != null
            })
            Log.d("kang","services:${rosServices.map { "${it.name}_${it.id}" }}")
            Log.d("kang", "services defined: ${needServices.size}, find:${rosServices.size} cost:${System.currentTimeMillis() - start}ms")
            checkTopicAndServiceReady()
        }else if ("serviceCallFailure" == op) {
            var resp = JSON.parseObject(message, ServiceCallFailure::class.java)
            Log.d("ROS","serviceFailure:$message")
            val maps = serviceObserver!![resp.serviceId]

            //debug
            if (maps == null) {
                Log.e("ROS", "maps == null ${resp.serviceId}")
            } else if (maps[resp.callId] == null) {
                Log.e("ROS", "maps.get(id) == null ${resp.callId}")
            }
            if (maps != null && maps[resp.callId] != null) {
                maps[resp.callId]!!.onError(-101, resp.message)
                maps.remove(resp.callId)
            }
        }
        //        if ("png".equals(op)) {
//            pngDataCache.offer(message);
//            Log.d(TAG, "parse cost:" + (parseTime - start));
//        } else if ("service_response".equals(op)) {
//            respDataCache.offer(message);
//            if (respDataCache.size() > 50) {
//                Log.e("ROS", "service_response queue will full");
//            }
//        }else if ("publish".equals(op) && ("/action_state".equals(topicBean.topic) || "/call_navigation_feedback".equals(topicBean.topic))){
//            actionDataCache.offer(message);
//            if (actionDataCache.size() > 50) {
//                Log.e("ROS", "action queue will full");
//            }
//        } else {
//            liteDataCache.offer(message);
//        }
    }
}
