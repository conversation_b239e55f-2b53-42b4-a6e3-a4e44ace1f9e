package com.iscas.rcljava.internal

import RosMsg.msg.Time
import com.iscas.rcljava.entity.topic.*
import com.iscas.rcljava.entity.topic.StringOccupancyGrid
import com.iscas.rcljava.entity.type.NodeNameInfo
import RosMsg.msg.*
import RosMsg.msg.MapMetaData
import RosMsg.msg.Odometry
import RosMsg.msg.Path
import RosMsg.msg.Armoriginalstate
import RosMsg.msg.Armstate
import RosMsg.msg.*
import RosMsg.msg.ActionState
import RosMsg.msg.BaseInfo
import RosMsg.msg.ControlArmState
import RosMsg.msg.JointState
import RosMsg.msg.LocalizationState
import RosMsg.msg.*
import RosMsg.msg.BatteryState
import RosMsg.msg.ExploreState
import RosMsg.msg.Imu
import RosMsg.msg.LaserScan
import RosMsg.msg.Range
import RosMsg.msg.Float32
import RosMsg.msg.Header
import RosMsg.msg.MqttState
import RosMsg.msg.Point
import RosMsg.msg.Pose
import RosMsg.msg.Quaternion
import RosMsg.msg.RoamState
import RosMsg.msg.RobotMonitorState
import RosMsg.msg.Twist
import RosMsg.msg.Vector3
import RosMsg.msg.VehicleInfo
import com.iscas.rcljava.entity.type.NameAndType

fun ControlArmState.convertJavaControlArmState(): com.iscas.rcljava.entity.topic.ControlArmState {
    return com.iscas.rcljava.entity.topic.ControlArmState().also {
        it.control_arm_state = this.control_arm_state
        it.control_arm_state_str = this.control_arm_state_str
    }
}
fun Armoriginalstate.convertJavaOriginalArmState(): ArmOriginalState {
    return ArmOriginalState().also {
        it.arm_err = this.arm_err
        it.sys_err = this.sys_err
        it.dof = this.dof
        it.jonit = this.joint.asList()
        it.pose = this.pose.asList()
    }
}
fun Armstate.convertJavaArmState(): ArmState {
    return ArmState().also {
        it.arm_err = this.arm_err
        it.sys_err = this.sys_err
        it.dof = this.dof
        it.jonit = this.joint.asList()
        it.pose = this.pose.convertJavaPose()
    }
}
//fun Inertia.convertJavaVisualRelocPose(): VisualRelocPose {
//    return VisualRelocPose().also{
//        it.com = this.com.convertJavaVector3()
//        it.ixx = this.ixx
//        it.ixy = this.ixy
//        it.ixz = this.ixz
//        it.iyy = this.iyy
//        it.iyz = this.iyz
//        it.izz = this.izz
//        it.m = this.m
//    }
//}

//fun RosMsg.msg.String.convertJavaString(): kotlin.String {
//    return this.data
//}
fun Header.convertJavaCalibrateStatus(): CalibrateStatus {
    return CalibrateStatus().also {
        it.frame_id = this.frame_id
        it.seq = this.stamp.sec
    }
}
//fun DepthMM.convertJavaShortPointCloud(): ShortPointCloud {
//    return ShortPointCloud().also {
//        it.time_ms = this.timeMs.toLong()
//        it.xyz = this.xyz
//    }
//}
fun PointCloud.convertJavaPointCloud(): com.iscas.rcljava.entity.base.PointCloud {
    return com.iscas.rcljava.entity.base.PointCloud().also {
        it.header = this.header.convertJavaHeader()
        it.points = this.points.map { it.convertJavaPoint() }.toTypedArray()
    }
}
fun Path.convertJavaPath(): com.iscas.rcljava.entity.topic.Path {
    return com.iscas.rcljava.entity.topic.Path().also {
        it.header = this.header.convertJavaHeader()
        it.poses = this.poses.map {
            var javaPoseStamped = com.iscas.rcljava.entity.base.PoseStamped().also { javaPoseStamped ->
                javaPoseStamped.pose = it.pose.convertJavaPose()
                javaPoseStamped.header = it.header.convertJavaHeader()
            }
            javaPoseStamped
        }.toTypedArray()
    }
}

//fun BaseDiagnostics.convertJavaDiagnostics(): DiagnosticsInfo{
//    return DiagnosticsInfo().also {
//        it.Bms_errCode = this.bmsErrCode
//        it.Bms_offline = this.bmsOffline
//        it.Chassis_offline = this.chassisOffline
//        it.Hum_offline = this.humOffline
//        it.Imu_offline = this.imuOffline
//        it.Motor_left_errCode = this.motorLeftErrCode
//        it.Motor_offline = this.motorOffline
//        it.Motor_right_errCode = this.motorRightErrCode
//        it.Sonar_offline = this.sonarOffline
//    }
//}
fun PoseStamped.convertJavaPoseStamped():com.iscas.rcljava.entity.base.PoseStamped{
    return com.iscas.rcljava.entity.base.PoseStamped().also {
        it.header = this.header.convertJavaHeader()
        it.pose = this.pose.convertJavaPose()
    }
}

fun Elevator.convertJavaElevator(): com.iscas.rcljava.entity.service.ElevatorInfo{
    return com.iscas.rcljava.entity.service.ElevatorInfo().also {
        it.device_id = this.device_id
        it.device_name = this.device_name
        it.reachable_floor = this.reachable_floor.toList()
        it.wait_pose = this.wait_pose.convertJavaVector3()
        it.take_pose = this.take_pose.convertJavaVector3()
        it.size = this.size.toList()
        it.polygon = this.polygon.convertJavaPolygon()
        it.floor_port_map = this.floor_port_map.toList()
    }
}
fun Region.convertJavaRegion(): com.iscas.rcljava.entity.service.Region {
    return com.iscas.rcljava.entity.service.Region().also {
        it.id = this.id
        it.labelName = this.label_name
        it.polygon = this.polygon.convertJavaPolygon()
    }
}

fun Polygon.convertJavaPolygon(): com.iscas.rcljava.entity.base.Polygon {
    return com.iscas.rcljava.entity.base.Polygon().also {
        it.points = this.points.map { point32 -> point32.convertJavaPoint() }.toTypedArray()
    }
}

fun Point32.convertJavaPoint(): com.iscas.rcljava.entity.base.Point {
    return com.iscas.rcljava.entity.base.Point().also {
        it.x = this.x.toDouble()
        it.y = this.y.toDouble()
        it.z = this.z.toDouble()
    }
}

fun ChassisState.convertJavaStateChassis(): com.iscas.rcljava.entity.topic.StateChassis {
    return com.iscas.rcljava.entity.topic.StateChassis().also {
        it.bat_percentage = this.bat_percentage
        it.chargingState = this.charging_state
        it.header = this.header.convertJavaHeader()
        it.localizationState = this.localization_state.convertJavaLocalizationState()
        it.setError_code(this.error_code)
    }
}

fun ElevatorState.convertJavaElevatorState(): com.iscas.rcljava.entity.topic.ElevatorState {
    return com.iscas.rcljava.entity.topic.ElevatorState().also {
        it.current_floor = this.current_floor
        it.door_status = this.door_status
        it.elevator_status = this.elevator_status
        it.msg = this.msg
        it.msg_code = this.msg_code
    }
}

fun Range.convertJavaRange(): com.iscas.rcljava.entity.topic.Range {
    return com.iscas.rcljava.entity.topic.Range().also {
        it.header = this.header.convertJavaHeader()
        it.radiation_type = this.radiation_type
        it.field_of_view = this.field_of_view
        it.max_range = this.max_range
        it.min_range = this.min_range
        it.range = this.range
    }
}

fun BaseInfo.convertJavaBaseInfo(): com.iscas.rcljava.entity.topic.BaseInfo {
    return com.iscas.rcljava.entity.topic.BaseInfo().also {
        it.connected = this.connected
        it.base_device_state = this.base_device_state.toShort()
        it.cliff_left = this.cliff_left
        it.cliff_front_left = this.cliff_front_left
        it.cliff_right = this.cliff_right
        it.cliff_front_right = this.cliff_front_right
        it.bump_state_left = this.bump_state_left
        it.bump_state_right = this.bump_state_right
        it.chargingVol = this.charging_vol
        it.chargingState = this.charging_state
        it.charge_ctrl = this.charge_ctrl.toShort()
        it.motor_left_err_code = this.motor_left_err_code
        it.motor_right_err_code = this.motor_right_err_code
        it.bump_enable = this.bump_enable
        it.cliff_enable = this.cliff_enable
        it.sonar_enable = this.sonar_enable
    }
}
fun ActionState.convertJavaActionState(): com.iscas.rcljava.entity.topic.ActionState {
    return com.iscas.rcljava.entity.topic.ActionState().also {
        it.action_feedback = this.action_feedback
        it.sub_action_feedback = this.sub_action_feedback
        it.action_state_id = this.action_state_id
        it.task_id = this.task_id
        it.feedback_text = this.feedback_text
        it.goal = this.goal.convertJavaVector3()
    }
}
fun JointState.convertJavaJonitState(): com.iscas.rcljava.entity.topic.JointStates {
    return JointStates().also {
        it.position = this.position.asList()
        it.name = this.name.asList()
        it.effort = this.effort.asList()
        it.velocity = this.velocity.asList()
    }
}
fun ExploreState.convertJavaExploreState(): com.iscas.rcljava.entity.topic.ExploreState {
    return com.iscas.rcljava.entity.topic.ExploreState().also {
        it.finished = this.finished
        it.frontiers = this.frontiers
        it.runningTime = this.running_time.sec
    }
}

fun RoamState.convertJavaRoamState(): com.iscas.rcljava.entity.topic.RoamState {
    return com.iscas.rcljava.entity.topic.RoamState().also {
        it.finished = this.finished
        it.frontiers = this.frontiers
        it.runningTime = this.running_time.sec
    }
}
fun ControlSkillResult.convertJavaSkillResult(): SkillResult {
    return SkillResult().also {
        it.error_info = this.error_info
        it.success = this.success
    }
}

fun RosMsg.msg.StringOccupancyGrid.convertJavaStringOccupancyGrid(): StringOccupancyGrid {
    return StringOccupancyGrid().also {
        it.angle = this.angle
        it.center = this.center.convertJavaPoint()
        it.data = this.data
        it.header = this.header.convertJavaHeader()
        it.height = this.height
        it.info = this.info.convertJavaMapMetaData()
        it.width = this.width
    }
}
fun BatteryState.convertJavaBatteryState(): com.iscas.rcljava.entity.topic.BatteryState{
    return com.iscas.rcljava.entity.topic.BatteryState().also {
        it.capacity = this.capacity
        it.cell_voltage = this.cell_voltage
        it.charge = this.charge
        it.current = this.current
        it.design_capacity = this.design_capacity
        it.header = this.header.convertJavaHeader()
        it.location = this.location
        it.percentage = this.percentage
        it.power_supply_health = this.power_supply_health
        it.power_supply_status = this.power_supply_status
        it.power_supply_technology = this.power_supply_technology
        it.present = this.present
        it.serial_number = this.serial_number
        it.voltage = this.voltage
    }
}
fun LaserScan.convertJavaLaserScan(): com.iscas.rcljava.entity.topic.LaserScan {
    return com.iscas.rcljava.entity.topic.LaserScan().also {
        it.angle_increment = this.angle_increment
        it.angle_max = this.angle_max
        it.angle_min = this.angle_min
        it.header = this.header.convertJavaHeader()
        it.intensities = this.intensities
        it.range_max = this.range_max
        it.range_min = this.range_min
        it.ranges = this.ranges
        it.scan_time = this.scan_time
        it.time_increment = this.time_increment
    }
}
fun Imu.convertJavaImu(): com.iscas.rcljava.entity.topic.Imu{
    return com.iscas.rcljava.entity.topic.Imu().also {
        it.header = this.header.convertJavaHeader()
        it.angular_velocity = this.angular_velocity.convertJavaVector3()
        it.angular_velocity_covariance = this.angular_velocity_covariance
        it.linear_acceleration = this.linear_acceleration.convertJavaVector3()
        it.linear_acceleration_covariance = this.linear_acceleration_covariance
        it.orientation = this.orientation.convertJavaQuaternion()
        it.orientation_covariance = this.orientation_covariance
    }
}

fun RosMsg.msg.OccupancyGrid.convertJavaOccupancyGrid(): com.iscas.rcljava.entity.topic.OccupancyGrid {
    return com.iscas.rcljava.entity.topic.OccupancyGrid().also {
        it.header = this.header.convertJavaHeader()
        it.data = this.data
        it.info = this.info.convertJavaMapMetaData()
    }
}
fun LocalizationState.convertJavaLocalizationState(): com.iscas.rcljava.entity.topic.LocalizationState {
    return com.iscas.rcljava.entity.topic.LocalizationState().also {
        it.header = this.header.convertJavaHeader()
        it.floor = this.floor
        it.global_pose = this.global_pose.convertJavaVector3()
        it.localization_state = this.localization_state
        it.map_name = this.map_name
        it.score = this.score
        it.progress = this.progress
    }
}

fun Odometry.convertJavaOdometry(): com.iscas.rcljava.entity.topic.Odometry {
    return com.iscas.rcljava.entity.topic.Odometry().also {
        it.child_frame_id = this.child_frame_id
        it.header = this.header.convertJavaHeader()
        it.pose = this.pose.convertJavaPoseWithCovariance()
        it.twist = this.twist.convertJavaTwistWithCovariance()
    }
}

fun Header.convertJavaHeader():com.iscas.rcljava.entity.base.Header{
    return com.iscas.rcljava.entity.base.Header().also {
        it.frame_id = this.frame_id
        it.stamp = this.stamp.convertJavaTime()
    }
}

fun Time.convertJavaTime():com.iscas.rcljava.entity.base.Time{
    return com.iscas.rcljava.entity.base.Time().also {
        it.nsecs = this.nanosec
        it.secs = this.sec
    }
}
fun Point.convertJavaPoint():com.iscas.rcljava.entity.base.Point{
    return com.iscas.rcljava.entity.base.Point().also {
        it.x = this.x
        it.y = this.y
        it.z = this.z
    }
}
fun Quaternion.convertJavaQuaternion():com.iscas.rcljava.entity.base.Quaternion{
    return com.iscas.rcljava.entity.base.Quaternion().also {
        it.w = this.w
        it.x = this.x
        it.y = this.y
        it.z = this.z
    }
}
fun Pose.convertJavaPose():com.iscas.rcljava.entity.base.Pose{
    return com.iscas.rcljava.entity.base.Pose().also {
        it.position = this.position.convertJavaPoint()
        it.orientation = this.orientation.convertJavaQuaternion()
    }
}
fun Vector3.convertJavaVector3():com.iscas.rcljava.entity.base.Vector3{
    return com.iscas.rcljava.entity.base.Vector3().also {
        it.x = this.x
        it.y = this.y
        it.z = this.z
    }
}

fun Twist.convertJavaTwist():com.iscas.rcljava.entity.base.Twist{
    return com.iscas.rcljava.entity.base.Twist().also {
        it.angular = this.angular.convertJavaVector3()
        it.linear = this.linear.convertJavaVector3()
    }
}

fun PoseWithCovariance.convertJavaPoseWithCovariance():com.iscas.rcljava.entity.base.PoseWithCovariance{
    return com.iscas.rcljava.entity.base.PoseWithCovariance().also {
        it.covariance = this.covariance
        it.pose = this.pose.convertJavaPose()
    }
}

fun TwistWithCovariance.convertJavaTwistWithCovariance():com.iscas.rcljava.entity.base.TwistWithCovariance{
    return com.iscas.rcljava.entity.base.TwistWithCovariance().also {
        it.covariance = this.covariance
        it.twist = this.twist.convertJavaTwist()
    }
}

fun MapMetaData.convertJavaMapMetaData(): com.iscas.rcljava.entity.base.MapMetaData {
    return com.iscas.rcljava.entity.base.MapMetaData().also {
        it.height = this.height
        it.map_load_time = this.map_load_time.convertJavaTime()
        it.origin = this.origin.convertJavaPose()
        it.width = this.width
        it.resolution = this.resolution
    }
}

fun VehicleInfo.convertJavaVehicleInfo(): com.iscas.rcljava.entity.topic.VehicleInfo {
     return com.iscas.rcljava.entity.topic.VehicleInfo().also {
         it.robot_vin = this.robot_vin
         it.client_passwd = this.client_passwd
         it.env = this.env
     }
}

fun MqttState.convertJavaMqttState(): com.iscas.rcljava.entity.topic.MqttState {
     return com.iscas.rcljava.entity.topic.MqttState().also {
         it.connect_state = this.connect_state
     }
}

fun RobotTaskInfo.convertJavaRobotTaskInfo(): com.iscas.rcljava.entity.topic.TaskInfo {
     return com.iscas.rcljava.entity.topic.TaskInfo().also {
         it.task_info = this.task_info
         it.travel_status = this.travel_status
     }
}

fun RobotMonitorState.convertJavaRobotMonitorState(): com.iscas.rcljava.entity.topic.RobotMonitorState {
     return com.iscas.rcljava.entity.topic.RobotMonitorState().also {
         it.sn = this.sn
         it.battery = this.battery
         it.locate_state = this.locate_state
         it.charging = this.charging
         it.point_x = this.point_x
         it.point_y =  this.point_y
         it.point_yaw = this.point_yaw
         it.emergency = this.emergency
         it.self_check_res = this.self_check_res
         it.motor_enabled = this.motor_enabled
     }
}