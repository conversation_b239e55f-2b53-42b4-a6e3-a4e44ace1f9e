package RosMsg.msg;



abstract public class RobotMonitorStateHelper
{
  private static String  _id = "IDL:RosMsg/msg/RobotMonitorState:1.0";

  public static void insert (org.omg.CORBA.Any a, RosMsg.msg.RobotMonitorState that)
  {
    org.omg.CORBA.portable.OutputStream out = a.create_output_stream ();
    a.type (type ());
    write (out, that);
    a.read_value (out.create_input_stream (), type ());
  }

  public static RosMsg.msg.RobotMonitorState extract (org.omg.CORBA.Any a)
  {
    return read (a.create_input_stream ());
  }

  private static org.omg.CORBA.TypeCode __typeCode = null;
  private static boolean __active = false;
  synchronized public static org.omg.CORBA.TypeCode type ()
  {
    if (__typeCode == null)
    {
      synchronized (org.omg.CORBA.TypeCode.class)
      {
        if (__typeCode == null)
        {
          if (__active)
          {
            return org.omg.CORBA.ORB.init().create_recursive_tc ( _id );
          }
          __active = true;
          org.omg.CORBA.StructMember[] _members0 = new org.omg.CORBA.StructMember [10];
          org.omg.CORBA.TypeCode _tcOf_members0 = null;
          _tcOf_members0 = org.omg.CORBA.ORB.init ().create_string_tc (0);
          _members0[0] = new org.omg.CORBA.StructMember (
            "sn",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_double);
          _members0[1] = new org.omg.CORBA.StructMember (
            "point_x",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_double);
          _members0[2] = new org.omg.CORBA.StructMember (
            "point_y",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_double);
          _members0[3] = new org.omg.CORBA.StructMember (
            "point_yaw",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_boolean);
          _members0[4] = new org.omg.CORBA.StructMember (
            "self_check_res",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_octet);
          _members0[5] = new org.omg.CORBA.StructMember (
            "battery",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_boolean);
          _members0[6] = new org.omg.CORBA.StructMember (
            "charging",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_boolean);
          _members0[7] = new org.omg.CORBA.StructMember (
            "emergency",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_boolean);
          _members0[8] = new org.omg.CORBA.StructMember (
            "locate_state",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().get_primitive_tc (org.omg.CORBA.TCKind.tk_boolean);
          _members0[9] = new org.omg.CORBA.StructMember (
            "motor_enabled",
            _tcOf_members0,
            null);
          __typeCode = org.omg.CORBA.ORB.init ().create_struct_tc (RosMsg.msg.RobotMonitorStateHelper.id (), "RobotMonitorState", _members0);
          __active = false;
        }
      }
    }
    return __typeCode;
  }

  public static String id ()
  {
    return _id;
  }

  public static RosMsg.msg.RobotMonitorState read (org.omg.CORBA.portable.InputStream istream)
  {
    RosMsg.msg.RobotMonitorState value = new RosMsg.msg.RobotMonitorState ();
    value.sn = istream.read_string ();
    value.point_x = istream.read_double ();
    value.point_y = istream.read_double ();
    value.point_yaw = istream.read_double ();
    value.self_check_res = istream.read_boolean ();
    value.battery = istream.read_octet ();
    value.charging = istream.read_boolean ();
    value.emergency = istream.read_boolean ();
    value.locate_state = istream.read_boolean ();
    value.motor_enabled = istream.read_boolean ();
    return value;
  }

  public static void write (org.omg.CORBA.portable.OutputStream ostream, RosMsg.msg.RobotMonitorState value)
  {
    ostream.write_string (value.sn);
    ostream.write_double (value.point_x);
    ostream.write_double (value.point_y);
    ostream.write_double (value.point_yaw);
    ostream.write_boolean (value.self_check_res);
    ostream.write_octet (value.battery);
    ostream.write_boolean (value.charging);
    ostream.write_boolean (value.emergency);
    ostream.write_boolean (value.locate_state);
    ostream.write_boolean (value.motor_enabled);
  }

}
