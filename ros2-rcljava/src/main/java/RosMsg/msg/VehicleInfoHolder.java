package RosMsg.msg;



public final class VehicleInfoHolder implements org.omg.CORBA.portable.Streamable
{
  public RosMsg.msg.VehicleInfo value = null;

  public VehicleInfoHolder ()
  {
  }

  public VehicleInfoHolder (RosMsg.msg.VehicleInfo initialValue)
  {
    value = initialValue;
  }

  public void _read (org.omg.CORBA.portable.InputStream i)
  {
    value = RosMsg.msg.VehicleInfoHelper.read (i);
  }

  public void _write (org.omg.CORBA.portable.OutputStream o)
  {
    RosMsg.msg.VehicleInfoHelper.write (o, value);
  }

  public org.omg.CORBA.TypeCode _type ()
  {
    return RosMsg.msg.VehicleInfoHelper.type ();
  }

}
