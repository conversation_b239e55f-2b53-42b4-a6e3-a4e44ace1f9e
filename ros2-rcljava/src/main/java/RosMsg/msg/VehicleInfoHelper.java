package RosMsg.msg;



abstract public class VehicleInfoHelper
{
  private static String  _id = "IDL:RosMsg/msg/VehicleInfo:1.0";

  public static void insert (org.omg.CORBA.Any a, RosMsg.msg.VehicleInfo that)
  {
    org.omg.CORBA.portable.OutputStream out = a.create_output_stream ();
    a.type (type ());
    write (out, that);
    a.read_value (out.create_input_stream (), type ());
  }

  public static RosMsg.msg.VehicleInfo extract (org.omg.CORBA.Any a)
  {
    return read (a.create_input_stream ());
  }

  private static org.omg.CORBA.TypeCode __typeCode = null;
  private static boolean __active = false;
  synchronized public static org.omg.CORBA.TypeCode type ()
  {
    if (__typeCode == null)
    {
      synchronized (org.omg.CORBA.TypeCode.class)
      {
        if (__typeCode == null)
        {
          if (__active)
          {
            return org.omg.CORBA.ORB.init().create_recursive_tc ( _id );
          }
          __active = true;
          org.omg.CORBA.StructMember[] _members0 = new org.omg.CORBA.StructMember [3];
          org.omg.CORBA.TypeCode _tcOf_members0 = null;
          _tcOf_members0 = org.omg.CORBA.ORB.init ().create_string_tc (0);
          _members0[0] = new org.omg.CORBA.StructMember (
            "robot_vin",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().create_string_tc (0);
          _members0[1] = new org.omg.CORBA.StructMember (
            "client_passwd",
            _tcOf_members0,
            null);
          _tcOf_members0 = org.omg.CORBA.ORB.init ().create_string_tc (0);
          _members0[2] = new org.omg.CORBA.StructMember (
            "env",
            _tcOf_members0,
            null);
          __typeCode = org.omg.CORBA.ORB.init ().create_struct_tc (RosMsg.msg.VehicleInfoHelper.id (), "VehicleInfo", _members0);
          __active = false;
        }
      }
    }
    return __typeCode;
  }

  public static String id ()
  {
    return _id;
  }

  public static RosMsg.msg.VehicleInfo read (org.omg.CORBA.portable.InputStream istream)
  {
    RosMsg.msg.VehicleInfo value = new RosMsg.msg.VehicleInfo ();
    value.robot_vin = istream.read_string ();
    value.client_passwd = istream.read_string ();
    value.env = istream.read_string ();
    return value;
  }

  public static void write (org.omg.CORBA.portable.OutputStream ostream, RosMsg.msg.VehicleInfo value)
  {
    ostream.write_string (value.robot_vin);
    ostream.write_string (value.client_passwd);
    ostream.write_string (value.env);
  }

}
