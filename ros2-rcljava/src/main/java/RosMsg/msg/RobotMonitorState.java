package RosMsg.msg;



public final class RobotMonitorState implements org.omg.CORBA.portable.IDLEntity
{
  public String sn = null;
  public double point_x = (double)0;
  public double point_y = (double)0;
  public double point_yaw = (double)0;
  public boolean self_check_res = false;
  public byte battery = (byte)0;
  public boolean charging = false;
  public boolean emergency = false;
  public boolean locate_state = false;
  public boolean motor_enabled = false;

  public RobotMonitorState ()
  {
  } // ctor

  public RobotMonitorState (String _sn, double _point_x, double _point_y, double _point_yaw, boolean _self_check_res, byte _battery, boolean _charging, boolean _emergency, boolean _locate_state, boolean _motor_enabled)
  {
    sn = _sn;
    point_x = _point_x;
    point_y = _point_y;
    point_yaw = _point_yaw;
    self_check_res = _self_check_res;
    battery = _battery;
    charging = _charging;
    emergency = _emergency;
    locate_state = _locate_state;
    motor_enabled = _motor_enabled;
  } // ctor

} // class RobotMonitorState
