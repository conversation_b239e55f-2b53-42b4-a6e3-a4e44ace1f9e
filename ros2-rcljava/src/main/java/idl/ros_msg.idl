#include "ros_base.idl"
module RosMsg {
    module msg {
        struct ActionState {
           string action_state_id;
           string task_id;
           Vector3 goal;
           int action_feedback;
           int sub_action_feedback;
           string feedback_text;
        };
        struct LocalizationState {
          Header header;
          uint localization_state;
          string map_name;
          int floor;
          Vector3 global_pose;
          float score;
          float progress;
        };
        struct BatteryState {
          Header header;
          float voltage;
          float temperature;
          float current;
          float charge;
          float capacity;
          float design_capacity;
          float percentage;
          octet power_supply_status;
          octet power_supply_health;
          octet power_supply_technology;
          boolean present;
          sequence<float> cell_voltage;
          sequence<float> cell_temperature;
          string location;
          string serial_number;
        };
        struct BaseInfo {
          boolean connected;
          octet base_device_state;
          boolean cliff_left;
          boolean cliff_front_left;
          boolean cliff_front_right;
          boolean cliff_right;
          boolean bump_state_left;
          boolean bump_state_right;
          short charging_vol;
          octet charging_state;
          octet charge_ctrl;
          short motor_left_err_code;
          short motor_right_err_code;
          boolean bump_enable;
          boolean cliff_enable;
          boolean sonar_enable;
        };
        struct ControlArmState {
          int control_arm_state;
          string control_arm_state_str;
        };
        struct Region {
          uint id;
          string label_name;
          sequence<Vector3> related_pose;
          Polygon polygon;
        };
        struct ChassisState {
          Header header;
          LocalizationState localization_state;
          octet charging_state;
          float bat_percentage;
          sequence<string> error_code;
        };
        struct Armstate {
          sequence<float> joint;
          Pose pose;
          short arm_err;
          short sys_err;
          octet dof;
        };
        struct Armoriginalstate {
          sequence<float> joint;
          float pose[6];
          short arm_err;
          short sys_err;
          octet dof;
        };
        struct StringOccupancyGrid {
          Header header;
          MapMetaData info;
          float angle;
          uint width;
          uint height;
          Point center;
          string data;
        };
        struct Gate {
          string device_name;
          string device_id;
          sequence<Vector3> gate_pose;
          sequence<Vector3> wait_pose;
        };
        struct Elevator {
          string device_name;
          string device_id;
          sequence<string> floor_port_map;
          sequence<int> reachable_floor;
          Vector3 wait_pose;
          Vector3 take_pose;
          sequence<float> size;
          Polygon polygon;
        };
        struct SwitchMapPoint {
          string point_name;
          string point_id;
          string map_id;
          Vector3 point_pose;
        };
        struct ExploreState {
          uint frontiers;
          Duration running_time;
          boolean finished;
        };
        struct RoamState {
          uint frontiers;
          Duration running_time;
          boolean finished;
        };
        struct VehicleInfo {
          string robot_vin;
          string client_passwd;
          string env;
        };
        struct RobotTaskInfo {
          string task_info;
          string travel_status;
        };
        struct MqttState {
          string connect_state;
        };
        struct RobotMonitorState {
            string sn;
            double point_x;
            double point_y;
            double point_yaw;
            boolean self_check_res;
            octet battery;
            boolean charging;
            boolean emergency;
            boolean locate_state;
            boolean motor_enabled;
        };
    };

};

/**
 * 灵巧手
 */
module RosMsg {
    module msg {
        struct Detection {
          string label_name;
          string label_id;
          Point32 point;
        };
    };
};

module RosMsg {
    module msg {
        struct Gripperset {
          short position;
          boolean block;
        };
        struct Movej {
          sequence<float> joint;
          octet speed;
          boolean block;
          octet trajectory_connect;
          octet dof;
        };
        struct Empty {
          octet structure_needs_at_least_one_member;
        };
    };
};

module RosMsg {
    module msg {
        struct ControlSkillResult {
          boolean success;
          string error_info;
        };
        struct Skill {
          string operation_name;
          string obj;
          string container;
          string relationship;
        };
    };
};