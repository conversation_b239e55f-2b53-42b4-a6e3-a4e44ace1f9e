#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

def replace_dimens_in_file(file_path):
    """替换单个文件中的dp和sp值"""
    
    # 定义需要替换的dp值
    dp_values = [787, 760, 659, 617, 616, 582, 573, 510, 433, 400, 380, 370, 368, 351, 350, 348, 345, 336, 335, 261, 250, 224, 216, 203, 200, 186, 160, 157, 155, 147, 145, 144, 140, 124, 122, 120, 111, 108, 107, 100, 96, 88, 87, 80, 75, 74, 73, 72, 64, 60, 56, 50, 48, 44, 42, 40, 36, 32, 31, 30, 26, 25, 24, 22, 21, 20, 18, 17, 16, 15, 14, 12, 10, 8, 6, 5, 3, 2]
    
    # 定义需要替换的sp值
    sp_values = [58, 56, 48, 42, 38, 32, 24, 22, 20]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换dp值（按长度降序，避免部分匹配）
        for dp in dp_values:
            pattern = r'\b' + str(dp) + r'dp\b'
            replacement = f'@dimen/dp_{dp}'
            content = re.sub(pattern, replacement, content)
        
        # 替换sp值（按长度降序，避免部分匹配）
        for sp in sp_values:
            pattern = r'\b' + str(sp) + r'sp\b'
            replacement = f'@dimen/sp_{sp}'
            content = re.sub(pattern, replacement, content)
        
        # 只有内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已处理: {file_path}")
            return True
        else:
            print(f"- 无变化: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 错误处理 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始替换layout文件中的dp和sp值...")
    
    # 获取所有layout文件
    layout_files = glob.glob('app/src/main/res/layout/*.xml')
    
    if not layout_files:
        print("未找到layout文件")
        return
    
    processed_count = 0
    total_count = len(layout_files)
    
    for file_path in layout_files:
        if replace_dimens_in_file(file_path):
            processed_count += 1
    
    print(f"\n替换完成！")
    print(f"总文件数: {total_count}")
    print(f"已处理文件数: {processed_count}")
    print(f"未变化文件数: {total_count - processed_count}")
    
    # 显示几个示例文件的前几行
    print("\n验证替换结果:")
    for file_path in layout_files[:2]:
        print(f"\n=== {os.path.basename(file_path)} 前5行 ===")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:5]
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line.rstrip()}")
        except Exception as e:
            print(f"读取文件错误: {e}")

if __name__ == "__main__":
    main()
